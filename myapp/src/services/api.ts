import { request } from '@umijs/max';

// API基础配置
const API_BASE_URL = 'http://192.168.200.31:30004/poc';

// 文件上传相关API
export const fileApi = {
  // 上传文件
  upload: (formData: FormData) => {
    return request(`${API_BASE_URL}/doc/upload`, {
      method: 'POST',
      data: formData,
      requestType: 'form',
    });
  },

  // 获取文件状态
  getFileStatus: (id: string) => {
    return request(`${API_BASE_URL}/doc/file-status`, {
      method: 'GET',
      params: { id },
    });
  },
};

// AI检测相关API
export const aiApi = {
  // AI检测
  aiCheck: (id: string) => {
    return request(`${API_BASE_URL}/doc/ai-check`, {
      method: 'GET',
      params: { id },
      timeout: 600000,
    });
  },

  // 获取AI检测状态
  getCheckStatus: (id: string) => {
    return request(`${API_BASE_URL}/doc/ai-check-status`, {
      method: 'GET',
      params: { id },
    });
  },

  // 获取授权应用列表
  getAuthAppList: () => {
    return request(`${API_BASE_URL}/doc/auth-app-list`, {
      method: 'GET',
    });
  },
};

// 规则管理相关API
export const ruleApi = {
  // 分页查询规则
  getPage: (params: { page: number; pageSize: number; keywords?: string }) => {
    return request(`${API_BASE_URL}/v2/rule/page`, {
      method: 'GET',
      params,
    });
  },

  // 批量发布规则
  batchPublish: (data: { idList: React.Key[]; status: number }) => {
    return request(`${API_BASE_URL}/v2/rule/batch-publish`, {
      method: 'POST',
      data,
    });
  },

  // 删除规则
  deleteById: (id: string) => {
    return request(`${API_BASE_URL}/v2/rule/deleteById`, {
      method: 'DELETE',
      params: { id },
    });
  },

  // 新增规则
  add: (data: any) => {
    return request(`${API_BASE_URL}/v2/rule/add`, {
      method: 'POST',
      data,
    });
  },

  // 更新规则
  update: (data: any) => {
    return request(`${API_BASE_URL}/v2/rule/update`, {
      method: 'POST',
      data,
    });
  },

  // 获取数据集
  fetchDataset: () => {
    return request(`${API_BASE_URL}/v2/rule/fetch-dataset`, {
      method: 'GET',
    });
  },

  // 选择数据集
  chooseDataset: (params: { id: string }) => {
    return request(`${API_BASE_URL}/v2/rule/choose-dataset`, {
      method: 'GET',
      params,
    });
  },

  // 规则自动补全
  ruleAutocomplete: (params: { ruleName: string }) => {
    return request(`${API_BASE_URL}/v2/rule/rule-autocomplete`, {
      method: 'GET',
      params,
    });
  },
};

// 统一导出所有API
export default {
  fileApi,
  aiApi,
  ruleApi,
};
