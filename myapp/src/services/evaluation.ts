import { request } from '@umijs/max';

/** 点赞评价接口 */
export async function aiEvaluation(
  data: {
    /** 评价对象ID */
    id: string;
    /** 评价状态 (0:未评价,1:准确,2:不准确) */
    isLike: 0 | 1 | 2;
  },
  options?: { [key: string]: any },
) {
  return request('http://192.168.200.31:30004/poc/doc/ai-evaluation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
    ...(options || {}),
  });
}
