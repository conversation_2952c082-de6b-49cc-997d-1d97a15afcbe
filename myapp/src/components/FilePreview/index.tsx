import { ExclamationCircleOutlined, InboxOutlined } from '@ant-design/icons';
import { Button, Card, Layout, message, Space, Spin, Upload } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { fileApi } from '../../services/api';
import DocumentParsingOverlay from './DocumentParsingOverlay';
import IntelligentDetection from './IntelligentDetection';
import ProcessSteps from './ProcessSteps';
declare global {
  interface Window {
    pdfjsLib: any;
    pdfjsDistBuildPdf?: any;
    PDFJS?: any;
    PDFViewerApplication?: {
      findController: {
        executeCommand: (cmd: string, params: any) => void;
      };
      eventBus: {
        dispatch: (event: string, data: any) => void;
      };
    };
  }
}

// CDN加载方案 - 优先使用
const loadCDNPDFJS = async (): Promise<any> => {
  console.log('使用本地PDF.js方案...');

  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    const existingLib =
      (window as any).pdfjsLib ||
      (window as any).pdfjsDistBuildPdf ||
      (window as any).PDFJS;
    if (existingLib && typeof existingLib.getDocument === 'function') {
      console.log('PDF.js已从本地加载');
      resolve(existingLib);
      return;
    }

    const script = document.createElement('script');
    // 修改为本地文件路径，保持版本号一致
    script.src = '/pdfjs/pdf.min.js';
    script.async = true;
    script.crossOrigin = 'anonymous';

    script.onload = () => {
      console.log('本地PDF.js脚本已加载');

      let attempts = 0;
      const maxAttempts = 50;

      const checkPDFJS = () => {
        attempts++;
        const pdfLib =
          (window as any).pdfjsLib ||
          (window as any).pdfjsDistBuildPdf ||
          (window as any).PDFJS;

        if (pdfLib && typeof pdfLib.getDocument === 'function') {
          console.log('本地PDF.js库已可用');
          window.pdfjsLib = pdfLib;
          if (pdfLib.GlobalWorkerOptions) {
            // 修改worker文件路径为本地路径
            pdfLib.GlobalWorkerOptions.workerSrc = '/pdfjs/pdf.worker.min.js';
          }
          resolve(pdfLib);
        } else if (attempts < maxAttempts) {
          setTimeout(checkPDFJS, 100);
        } else {
          console.error('本地PDF.js加载超时');
          reject(new Error('本地PDF.js加载超时'));
        }
      };

      checkPDFJS();
    };

    script.onerror = (error) => {
      console.error('本地PDF.js脚本加载失败:', error);
      reject(error);
    };

    document.head.appendChild(script);
  });
};

// 本地备用方案
const loadLocalPDFJS = async (): Promise<any> => {
  console.log('尝试本地PDF.js备用方案...');

  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    const existingLib =
      (window as any).pdfjsLib ||
      (window as any).pdfjsDistBuildPdf ||
      (window as any).PDFJS;
    if (existingLib && typeof existingLib.getDocument === 'function') {
      console.log('PDF.js已从本地加载');
      resolve(existingLib);
      return;
    }

    const script = document.createElement('script');
    script.src = '/pdf.js';

    script.onload = () => {
      console.log('本地PDF.js脚本已加载');

      let attempts = 0;
      const maxAttempts = 30;

      const checkPDFJS = () => {
        attempts++;
        const pdfLib =
          (window as any).pdfjsLib ||
          (window as any).pdfjsDistBuildPdf ||
          (window as any).PDFJS;

        if (pdfLib && typeof pdfLib.getDocument === 'function') {
          console.log('本地PDF.js库已可用');
          window.pdfjsLib = pdfLib;
          if (pdfLib.GlobalWorkerOptions) {
            pdfLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.js';
          }
          resolve(pdfLib);
        } else if (attempts < maxAttempts) {
          setTimeout(checkPDFJS, 100);
        } else {
          console.error('本地PDF.js加载超时');
          reject(new Error('本地PDF.js加载超时'));
        }
      };

      checkPDFJS();
    };

    script.onerror = (error) => {
      console.error('本地PDF.js脚本加载失败:', error);
      reject(error);
    };

    document.head.appendChild(script);
  });
};

// 主加载函数 - CDN优先，本地备用
const loadPDFJS = async (): Promise<any> => {
  // 检查是否已经加载
  if (window.pdfjsLib && typeof window.pdfjsLib.getDocument === 'function') {
    return window.pdfjsLib;
  }

  try {
    console.log('开始加载PDF.js (CDN优先)...');
    return await loadCDNPDFJS();
  } catch (error) {
    console.warn('CDN加载失败，尝试本地备用方案:', error);
    try {
      return await loadLocalPDFJS();
    } catch (localError) {
      console.error('所有PDF.js加载方案都失败:', localError);
      throw new Error('PDF.js加载失败');
    }
  }
};

interface PDFDocumentProxy {
  numPages: number;
  getPage: (pageNumber: number) => Promise<any>;
  destroy: () => void;
}
// @ts-ignore
import styles from './index.less';

const { Content } = Layout;
// const { Title, Paragraph } = Typography;
const { Dragger } = Upload;

export interface UploadResult {
  id: string;
  originFileName: string;
  fileMd5: string;
  fileSize: number;
  uploadedFileName: string;
  uploadedFileUrl: string;
  createTime: string;
}

export interface FilePreviewProps {
  file?: File;
  uploadResult?: UploadResult;
  onFileSelect?: (file: File) => void;
}

const FilePreview: React.FC<FilePreviewProps> = ({
  file,
  uploadResult,
  onFileSelect,
}): JSX.Element => {
  const [loading, setLoading] = useState(false);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [showRightPanel, setShowRightPanel] = useState(false);
  const [uploadTime, setUploadTime] = useState<string>('');
  const [fileStatus, setFileStatus] = useState<number | null>(null);
  const [statusDesc, setStatusDesc] = useState<string>('正在解析...');
  const [step2Percent, setStep2Percent] = useState<number>(0);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [uploadStatus, setUploadStatus] = useState<string>('上传中...');
  const [detectStatus, setDetectStatus] = useState<string>('待检测');
  // const fileInputRef = useRef<HTMLInputElement>(null);
  const [pdfDoc, setPdfDoc] = useState<PDFDocumentProxy | null>(null);
  const pdfDocumentRef = useRef<PDFDocumentProxy | null>(null);

  const [pdfError, setPdfError] = useState<string | null>(null);
  const [showParsingOverlay, setShowParsingOverlay] = useState(false);
  const [isJumping, setIsJumping] = useState(false);

  // 添加PDF文档加载函数
  const loadPDFDocument = async (url?: string) => {
    try {
      setPdfError(null);
      setLoading(true);

      const pdfLib = await loadPDFJS();

      if (!pdfLib || typeof pdfLib.getDocument !== 'function') {
        throw new Error('PDF.js库未正确加载');
      }

      const targetUrl = url || pdfUrl;
      if (!targetUrl) {
        throw new Error('PDF URL不可用');
      }

      const loadingTask = pdfLib.getDocument({
        url: targetUrl,
        cMapUrl: '/pdfjs/cmaps/',
        cMapPacked: true,
        disableAutoFetch: true,
        disableStream: true,
      });

      const pdfDocument = await loadingTask.promise;
      pdfDocumentRef.current = pdfDocument;

      console.log('PDF文档加载成功');
      return pdfDocument;
    } catch (error) {
      console.error('PDF加载失败:', error);
      setPdfError(error instanceof Error ? error.message : 'PDF加载失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let isMounted = true;
    let cleanupTimer: NodeJS.Timeout;
    let loading = false;

    const loadPdf = async (file: File) => {
      if (loading) return;
      loading = true;
      try {
        if (!isMounted) return;

        setLoading(true);
        const blob = new Blob([file], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        setPdfUrl(url);
        setUploadTime(new Date().toLocaleString('zh-CN'));

        try {
          setPdfError(null);
          const pdfLib = await loadPDFJS();
          if (!pdfLib || typeof pdfLib.getDocument !== 'function') {
            throw new Error('PDF.js库未正确加载');
          }
          const loadingTask = pdfLib.getDocument({
            url,
            cMapUrl: '/pdfjs/cmaps/',
            cMapPacked: true,
            disableAutoFetch: true,
            disableStream: true,
            verbosity: 0,
            maxImageSize: -1,
          });

          loadingTask.onProgress = ({
            loaded,
            total,
          }: {
            loaded: number;
            total: number;
          }) => {
            console.log(`加载进度: ${Math.round((loaded / total) * 100)}%`);
          };

          const doc = await loadingTask.promise;
          if (!isMounted) return;

          if (!doc || !doc.numPages) {
            throw new Error('PDF文档无效或损坏');
          }

          const firstPage = await doc.getPage(1);
          if (!firstPage) {
            throw new Error('无法获取PDF页面');
          }

          setPdfDoc(doc);
          pdfDocumentRef.current = doc;
          console.error('PDF文档加载完成，pdfDoc已设置:', doc !== null);
          console.error('PDF文档详细信息:', {
            numPages: doc.numPages,
            fingerprint: doc.fingerprint,
            loaded: true,
          });
          console.log(`PDF文档加载成功，总页数:`, doc.numPages);
        } catch (err: unknown) {
          const error = err instanceof Error ? err : new Error(String(err));
          console.error('PDF加载失败 - 详细错误:', {
            error: error.message,
            stack: error.stack,
            url: url,
            time: new Date().toISOString(),
          });
          setPdfError(error.message);
          message.error(`PDF加载失败: ${error.message}`);
          if (isMounted) {
            setPdfDoc(null);
            setPdfUrl(url);
          }
        }
      } catch (err: unknown) {
        const error = err instanceof Error ? err : new Error(String(err));
        console.error('PDF加载失败:', error);
        setPdfError(error.message);
        message.error(`PDF文档加载失败: ${error.message}`);
        if (isMounted) setPdfDoc(null);
      } finally {
        if (isMounted) {
          cleanupTimer = setTimeout(() => {
            setLoading(false);
            setShowRightPanel(true);
          }, 500);
        }
      }
    };

    if (uploadResult) {
      setLoading(true);
      setPdfUrl(uploadResult.uploadedFileUrl);
      setUploadTime(new Date(uploadResult.createTime).toLocaleString('zh-CN'));
      setUploadStatus('上传成功');
      setShowRightPanel(true);
      setCurrentStep(2);
      setShowParsingOverlay(false);
      setStep2Percent(100);
      setLoading(false);
    } else if (file) {
      loadPdf(file);
    } else {
      setShowRightPanel(false);
      setUploadTime('');
    }

    return () => {
      isMounted = false;
      loading = false;
      clearTimeout(cleanupTimer);
      if (pdfDoc) {
        try {
          pdfDoc.destroy();
        } catch (error) {
          console.warn('PDF文档销毁失败:', error);
        }
      }
      if (pdfDocumentRef.current) {
        try {
          pdfDocumentRef.current.destroy();
          pdfDocumentRef.current = null;
        } catch (error) {
          console.warn('PDF文档引用销毁失败:', error);
        }
      }
    };
  }, [file, uploadResult]);

  const handleUpload = (file: File) => {
    if (file.size > 100 * 1024 * 1024) {
      message.warning('文件大小不能超过100MB');
      return false;
    }
    if (file.type !== 'application/pdf') {
      message.warning('请上传 PDF 文件');
      return false;
    }
    onFileSelect?.(file);
    return false;
  };

  const truncateFileName = (fileName: string) => {
    if (fileName.length <= 20) return fileName;
    const ext = fileName.lastIndexOf('.');
    const name = ext !== -1 ? fileName.slice(0, ext) : fileName;
    const extension = ext !== -1 ? fileName.slice(ext) : '';
    return `${name.slice(0, 8)}...${name.slice(-6)}${extension}`;
  };

  // 新增字符串清洗函数
  function cleanString(str: string) {
    return str.replace(/[\s\p{P}]/gu, '').toLowerCase();
  }

  // 新增分词函数（简单按中文、英文、数字分割）
  function splitWords(str: string) {
    // 可根据实际情况替换为更强分词算法
    return str.match(/([\u4e00-\u9fa5]+|[a-zA-Z0-9]+)/g) || [];
  }

  // 统一的PDF文本搜索函数
  const findTextInPDF = async (searchText: string) => {
    try {
      const pdfLib = await loadPDFJS();
      if (!pdfLib || typeof pdfLib.getDocument !== 'function') {
        throw new Error('PDF.js库未正确加载');
      }

      if (!pdfDocumentRef.current) {
        // 如果PDF文档未加载，先加载
        const pdfDocument = await loadPDFDocument(pdfUrl);
        pdfDocumentRef.current = pdfDocument;
      }

      const pdfDocument = pdfDocumentRef.current;
      if (!pdfDocument) {
        throw new Error('PDF文档未加载');
      }

      console.log(`开始在${pdfDocument.numPages}页中搜索文本:`, searchText);

      // 遍历所有页面查找文本
      for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
        try {
          const page = await pdfDocument.getPage(pageNum);
          const textContent = await page.getTextContent();

          // 改进文本提取方式
          const pageText = textContent.items
            .map((item: any) => item.str)
            .join('') // 不使用空格连接，保持原始文本结构
            .replace(/\s+/g, '') // 移除所有空白字符
            .toLowerCase();

          // 同样处理搜索文本
          const cleanSearchText = searchText
            .replace(/\s+/g, '') // 移除所有空白字符
            .toLowerCase();

          console.log(`第${pageNum}页文本片段:`, pageText.substring(0, 100));
          console.log(`搜索文本:`, cleanSearchText);

          // 检查是否包含搜索文本
          if (pageText.includes(cleanSearchText)) {
            console.log(`在第${pageNum}页找到文本`);
            return {
              pageNumber: pageNum,
              text: searchText,
            };
          }

          // 额外尝试：模糊匹配（去除标点符号）
          const fuzzyPageText = pageText.replace(/[^\u4e00-\u9fa5\w]/g, '');
          const fuzzySearchText = cleanSearchText.replace(
            /[^\u4e00-\u9fa5\w]/g,
            '',
          );

          if (fuzzyPageText.includes(fuzzySearchText)) {
            console.log(`在第${pageNum}页找到模糊匹配文本`);
            return {
              pageNumber: pageNum,
              text: searchText,
            };
          }
        } catch (pageError) {
          console.warn(`处理第${pageNum}页时出错:`, pageError);
          continue;
        }
      }

      return null;
    } catch (error) {
      console.error('PDF文本搜索失败:', error);
      throw error;
    }
  };

  // 增强的页面跳转函数 - 针对Chrome、Firefox和Edge优化
  const handlePageJump = (page: number) => {
    if (!pdfUrl) return;

    try {
      const iframe = document.querySelector(
        'iframe.pdfViewer',
      ) as HTMLIFrameElement;

      if (!iframe) {
        console.warn('未找到PDF iframe元素');
        return;
      }

      const baseUrl = pdfUrl.split('#')[0].split('?')[0];
      const newUrl = `${baseUrl}#page=${page}`;

      console.log(`跳转到第${page}页，新URL: ${newUrl}`);

      // 检测浏览器类型
      const userAgent = navigator.userAgent.toLowerCase();
      const isChrome =
        userAgent.indexOf('chrome') > -1 && userAgent.indexOf('edge') === -1;
      const isFirefox = userAgent.indexOf('firefox') > -1;
      const isEdge = userAgent.indexOf('edge') > -1;

      // Chrome和Firefox浏览器处理 - 使用相同的iframe替换策略
      if (isChrome || isFirefox) {
        try {
          // 创建新的iframe
          const newIframe = document.createElement('iframe');
          newIframe.className = 'pdfViewer';
          newIframe.src = newUrl;
          newIframe.style.width = '100%';
          newIframe.style.height = '100%';
          newIframe.style.border = 'none';

          // 复制原有iframe的所有属性
          Array.from(iframe.attributes).forEach((attr) => {
            if (attr.name !== 'src') {
              newIframe.setAttribute(attr.name, attr.value);
            }
          });

          // 添加加载事件监听
          newIframe.onload = () => {
            console.log(`${isChrome ? 'Chrome' : 'Firefox'}: iframe加载完成`);
          };

          // 替换原有iframe
          if (iframe.parentNode) {
            iframe.parentNode.replaceChild(newIframe, iframe);
            console.log(
              `${isChrome ? 'Chrome' : 'Firefox'}: 使用iframe替换方式跳转`,
            );
            return;
          }
        } catch (error) {
          console.warn(
            `${isChrome ? 'Chrome' : 'Firefox'} iframe替换失败，尝试其他方案:`,
            error,
          );
        }
      }

      // Edge浏览器处理
      if (isEdge) {
        try {
          if (iframe.contentWindow) {
            iframe.contentWindow.location.href = newUrl;
            console.log('Edge: 使用location.href跳转');
            return;
          }
        } catch (error) {
          console.warn('Edge跳转失败，尝试其他方案:', error);
        }
      }

      // 通用降级方案：创建新的iframe
      try {
        const newIframe = document.createElement('iframe');
        newIframe.className = 'pdfViewer';
        newIframe.src = newUrl;
        newIframe.style.width = '100%';
        newIframe.style.height = '100%';
        newIframe.style.border = 'none';

        // 复制原有iframe的所有属性
        Array.from(iframe.attributes).forEach((attr) => {
          if (attr.name !== 'src') {
            newIframe.setAttribute(attr.name, attr.value);
          }
        });

        // 替换原有iframe
        if (iframe.parentNode) {
          iframe.parentNode.replaceChild(newIframe, iframe);
          console.log('使用iframe替换方式跳转');
          return;
        }
      } catch (error) {
        console.warn('iframe替换失败，尝试最后方案:', error);
      }

      // 最后的降级方案：直接修改src
      try {
        iframe.src = newUrl;
        console.log('使用直接修改src方式跳转');
      } catch (error) {
        console.error('所有跳转方案都失败:', error);
        message.error('页面跳转失败，请刷新页面后重试');
      }
    } catch (error) {
      console.error('PDF页面跳转失败:', error);
      message.error('页面跳转失败，请刷新页面后重试');
    }
  };

  // 优化后的内容跳转函数
  const handleContentJump = async (content: string) => {
    // 防止重复点击
    if (isJumping) {
      message.warning('正在跳转中，请稍候...');
      return;
    }

    try {
      setIsJumping(true);
      message.loading('正在搜索文档内容，请稍候...', 0);
      console.log('开始跳转到内容:', content);
      let pageInfo = null;

      // 1. 先尝试完整原文查找
      pageInfo = await findTextInPDF(content);

      // 2. 去除空白和标点后查找
      if (!pageInfo) {
        const cleanText = cleanString(content);
        pageInfo = await findTextInPDF(cleanText);
      }

      // 3. 再降级用前12字查找
      if (!pageInfo && content.length > 12) {
        pageInfo = await findTextInPDF(content.slice(0, 12));
      }

      // 4. 分词递减查找
      if (!pageInfo) {
        const words = splitWords(content);
        for (let i = 0; i < words.length; i++) {
          // 从最长词组开始递减查找
          const sub = words.slice(i).join('');
          if (sub.length >= 4) {
            // 只查长度大于等于4的
            pageInfo = await findTextInPDF(sub);
            if (pageInfo) break;
          }
        }
      }

      // 5. 最后模糊包含查找（遍历每页文本，包含即算）
      if (!pageInfo) {
        const pdfLib = await loadPDFJS();
        if (!pdfLib || typeof pdfLib.getDocument !== 'function') {
          throw new Error('PDF.js库未正确加载');
        }
        if (!pdfDocumentRef.current) {
          const pdfDocument = await loadPDFDocument(pdfUrl);
          pdfDocumentRef.current = pdfDocument;
        }
        const pdfDocument = pdfDocumentRef.current;
        if (pdfDocument) {
          for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
            try {
              const page = await pdfDocument.getPage(pageNum);
              const textContent = await page.getTextContent();
              const pageText = textContent.items
                .map((item: any) => item.str)
                .join('')
                .replace(/\s+/g, '')
                .toLowerCase();
              if (pageText.includes(content.slice(0, 4).toLowerCase())) {
                pageInfo = { pageNumber: pageNum, text: content };
                break;
              }
            } catch {}
          }
        }
      }

      if (pageInfo) {
        console.log(`找到文本在第${pageInfo.pageNumber}页`);
        handlePageJump(pageInfo.pageNumber);
        message.destroy(); // 隐藏loading消息
        message.success(`已跳转到第${pageInfo.pageNumber}页`);
      } else {
        console.log('未找到匹配的文本内容');
        message.destroy(); // 隐藏loading消息
        message.warning('未找到匹配的文本内容');
      }
    } catch (error) {
      console.error('跳转失败:', error);
      message.destroy(); // 隐藏loading消息
      message.error('跳转失败，请重试');
    } finally {
      setIsJumping(false); // 重置跳转状态
    }
  };

  return (
    <>
      {/* 文档解析遮罩组件 */}
      {/* <DocumentParsingOverlay
        visible={showParsingOverlay}
        statusDesc={statusDesc}
        progress={step2Percent}
      /> */}

      {/* 步骤条组件 */}
      <ProcessSteps
        currentStep={currentStep}
        step2Percent={step2Percent}
        uploadStatus={uploadStatus}
        statusDesc={statusDesc}
        detectStatus={detectStatus}
        showRightPanel={showRightPanel}
        uploadedFileUrl={pdfUrl}
      />

      <Layout className={styles.container}>
        <Content
          className={styles.content}
          style={{ marginRight: showRightPanel ? 1 : 0 }}
        >
          <Card
            styles={{ body: { padding: 0, height: '100%' } }}
            style={{ flex: 1, marginTop: showRightPanel ? 1 : 0 }}
          >
            {loading ? (
              <div
                style={{
                  height: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Spin size="large" />
              </div>
            ) : file ? (
              <div
                style={{ position: 'relative', width: '100%', height: '100%' }}
              >
                {pdfError ? (
                  <div
                    style={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      color: '#ff4d4f',
                      padding: '24px',
                    }}
                  >
                    <ExclamationCircleOutlined
                      style={{ fontSize: 48, marginBottom: 16 }}
                    />
                    <div style={{ fontSize: 16, marginBottom: 8 }}>
                      PDF加载失败
                    </div>
                    <div
                      style={{
                        fontSize: 14,
                        color: '#666',
                        textAlign: 'center',
                      }}
                    >
                      {pdfError}
                    </div>
                    <Button
                      type="primary"
                      style={{ marginTop: 16 }}
                      onClick={() => {
                        if (pdfUrl) {
                          loadPDFDocument(pdfUrl);
                        }
                      }}
                    >
                      重新加载
                    </Button>
                  </div>
                ) : (
                  <>
                    <iframe
                      key={pdfUrl}
                      src={pdfUrl}
                      className={`${styles.pdfViewer} pdfViewer`}
                      title="PDF预览"
                      style={{ width: '100%', height: '100%', border: 'none' }}
                    />
                  </>
                )}
              </div>
            ) : (
              <>
                <Dragger
                  accept=".pdf,application/pdf"
                  showUploadList={false}
                  beforeUpload={handleUpload}
                  className={styles.dragger}
                >
                  <div className={styles.uploadContent}>
                    <div className={styles.uploadIcon}>
                      <InboxOutlined
                        style={{ color: '#1677ff', fontSize: 48 }}
                      />
                    </div>
                    <p className={styles.uploadText}>
                      <span style={{ fontSize: 16, fontWeight: 500 }}>
                        点击上传文件
                      </span>
                      <span style={{ margin: '0 8px' }}>或</span>
                      <span style={{ fontSize: 16, fontWeight: 500 }}>
                        将文件拖拽到此处
                      </span>
                    </p>
                    <p className={styles.uploadHint}>
                      <span style={{ color: '#666' }}>仅支持单个 PDF 文件</span>
                      <span style={{ margin: '0 8px', color: '#666' }}>·</span>
                      <span style={{ color: '#666' }}>不超过 10MB</span>
                    </p>
                  </div>
                </Dragger>
                {uploadTime && (
                  <div
                    style={{
                      marginTop: 16,
                      padding: 16,
                      background: '#fafafa',
                      borderRadius: 8,
                      border: '1px dashed #d9d9d9',
                    }}
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ fontWeight: 500 }}>最近上传记录</div>
                      <div>
                        <span style={{ color: '#000000d9' }}>
                          {file
                            ? truncateFileName((file as File).name)
                            : uploadResult?.originFileName || ''}
                        </span>
                        <span style={{ color: '#00000073', marginLeft: 8 }}>
                          {uploadTime}
                        </span>
                      </div>
                    </Space>
                  </div>
                )}
              </>
            )}
          </Card>
        </Content>

        {showRightPanel && (
          <IntelligentDetection
            uploadResultId={uploadResult?.id}
            fileStatus={fileStatus}
            detectStatus={detectStatus}
            onDetectStatusChange={setDetectStatus}
            onContentJump={handleContentJump}
            onPageJump={handlePageJump}
            pdfUrl={pdfUrl}
            isJumping={isJumping}
          />
        )}
      </Layout>
    </>
  );
};

export default FilePreview;
