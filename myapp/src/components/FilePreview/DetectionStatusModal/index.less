.detection-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
}

.detection-modal {
  background: white;
  border-radius: 8px;
  padding: 32px;
  min-width: 400px;
  max-width: 600px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease-out;
}

.detection-status-modal {
  .ant-modal-content {
    padding: 24px;
  }

  .status-item {
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 8px;
    background-color: #f5f5f5;
    transition: all 0.3s;

    &.completed {
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
    }

    &.processing {
      background-color: #fffbe6;
      border: 1px solid #ffe58f;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(250, 173, 20, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0);
  }
}