import { CheckCircleTwoTone } from '@ant-design/icons';
import { <PERSON><PERSON>, Spin } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { aiApi } from '../../../services/api';
import './index.less';

interface StatusItem {
  ruleId: string;
  ruleName: string;
  status: string;
  statusCnDesc: string;
}

interface DetectionStatusModalProps {
  open: boolean;
  uploadResultId?: string;
  onComplete: () => void;
  onError: (error: string) => void;
}

const DetectionStatusModal: React.FC<DetectionStatusModalProps> = ({
  open,
  uploadResultId,
  onComplete,
  onError,
}) => {
  const [checkingStatus, setCheckingStatus] = useState<StatusItem[]>([]);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const pollCheckStatus = async (id: string) => {
    try {
      const result = await aiApi.getCheckStatus(id);
      if (result.success && result.result) {
        // 检查status字段，如果是notReady则显示数据准备中
        if (result.result.status === 'notReady') {
          setCheckingStatus([
            {
              ruleId: 'preparing',
              ruleName: '数据正在准备中...',
              status: 'INIT',
              statusCnDesc: '检测初始化中',
            },
          ]);
          return false;
        }

        // status为ready时，处理itemList数据
        if (result.result.status === 'ready' && result.result.itemList) {
          const statusItems: StatusItem[] = result.result.itemList.map(
            (item: any) => ({
              ruleId: item.ruleId,
              ruleName: item.ruleName,
              status: item.status,
              statusCnDesc: item.statusCnDesc,
            }),
          );

          setCheckingStatus(statusItems);

          // 检查是否所有项目都完成
          const allCompleted = result.result.itemList.every(
            (item: any) => item.status === 'MULTI_CHECKING_DONE',
          );

          if (allCompleted) {
            // 所有检测完成，通知父组件
            onComplete();
            return true;
          }
        }
      }
      return false;
    } catch (error) {
      console.error('状态轮询失败:', error);
      onError('状态查询失败');
      return false;
    }
  };

  useEffect(() => {
    if (open && uploadResultId) {
      // 立即执行一次检查
      pollCheckStatus(uploadResultId);

      // 开始轮询
      pollIntervalRef.current = setInterval(async () => {
        try {
          const isCompleted = await pollCheckStatus(uploadResultId);
          if (isCompleted) {
            if (pollIntervalRef.current) {
              clearInterval(pollIntervalRef.current);
              pollIntervalRef.current = null;
            }
          }
        } catch (error) {
          console.error('轮询检查失败:', error);
          if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current);
            pollIntervalRef.current = null;
          }
          onError('检测状态查询失败');
        }
      }, 2000); // 调整为2秒轮询一次

      // 设置超时
      timeoutRef.current = setTimeout(() => {
        if (pollIntervalRef.current) {
          clearInterval(pollIntervalRef.current);
          pollIntervalRef.current = null;
        }
        onError('检测超时，请重试');
      }, 10 * 60 * 1000); // 5分钟超时
    } else {
      // 清理轮询
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      setCheckingStatus([]);
    }

    // 清理函数
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [open, uploadResultId, onComplete, onError]);

  return (
    <Modal
      open={open}
      footer={null}
      closable={false}
      maskClosable={false}
      keyboard={false}
      width={600}
      centered
      className="detection-status-modal"
    >
      <div style={{ textAlign: 'center', marginBottom: 24 }}>
        <Spin size="large" />
        <div style={{ fontSize: 18, fontWeight: 'bold', marginTop: 16 }}>
          进行中
        </div>
        <div style={{ fontSize: 14, color: '#666', marginTop: 8 }}>
          系统正在通过
          <a
            href="http://rule.evermodel-dev.ai/home"
            target="_blank"
            style={{
              backgroundColor: '#eaf2ff',
              color: '#5499ff',
              padding: '3px 5px',
              borderRadius: 4,
              margin: '0 4px',
            }}
          >
            公平竞争审查工作流
          </a>
          进行内容的分析，请耐心等待...
        </div>
      </div>

      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {checkingStatus.map((item: StatusItem, index: number) => {
          const isCompleted = item.status === 'MULTI_CHECKING_DONE';
          const isProcessing =
            item.status?.includes('CHECKING') && !isCompleted;

          return (
            <div
              key={item.ruleId || index}
              className={`status-item ${
                isCompleted ? 'completed' : isProcessing ? 'processing' : ''
              }`}
            >
              <div
                style={{
                  fontWeight: 'bold',
                  marginBottom: 8,
                  fontSize: 14,
                }}
              >
                {item.ruleName}
              </div>
              <div
                style={{
                  color: isCompleted
                    ? '#52c41a'
                    : isProcessing
                    ? '#faad14'
                    : '#1890ff',
                  fontSize: 13,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 8,
                }}
              >
                {isProcessing && <Spin size="small" />}
                {isCompleted && <CheckCircleTwoTone twoToneColor="#52c41a" />}
                状态：{item.statusCnDesc}
              </div>
            </div>
          );
        })}
      </div>
    </Modal>
  );
};

export default DetectionStatusModal;
