import React, { useEffect, useState, useRef } from 'react';
import './index.less';

interface DocumentParsingOverlayProps {
  visible: boolean;
  statusDesc: string;
  progress: number;
}

const DocumentParsingOverlay: React.FC<DocumentParsingOverlayProps> = ({
  visible,
  statusDesc,
  progress,
}) => {
  const [localProgress, setLocalProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [initialProgress, setInitialProgress] = useState(0);
  const hasInitialized = useRef(false);
  const [currentEmoji, setCurrentEmoji] = useState('📄');

  // 处理初始进度过渡
  useEffect(() => {
    if (visible && isInitializing && !hasInitialized.current) {
      const targetProgress = 20; // 目标进度为20%
      const duration = 5000; // 5秒完成初始过渡
      const startTime = Date.now();
      const startProgress = 0;

      const animate = () => {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数使动画更自然
        const easeProgress = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        const newProgress = startProgress + (targetProgress - startProgress) * easeProgress;
        setInitialProgress(Math.round(newProgress));

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          setIsInitializing(false);
          hasInitialized.current = true;
          setLocalProgress(20); // 确保实际进度从20开始
        }
      };

      requestAnimationFrame(animate);
    }
  }, [visible]);

  // 处理实际进度过渡
  useEffect(() => {
    if (visible && !isInitializing && hasInitialized.current) {
      const targetProgress = Math.min(progress, 100);
      const duration = 500;
      const startTime = Date.now();
      const startProgress = localProgress;

      const animate = () => {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const easeProgress = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        const newProgress = startProgress + (targetProgress - startProgress) * easeProgress;
        setLocalProgress(Math.round(newProgress));

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);
    }
  }, [progress, visible, isInitializing]);

  useEffect(() => {
    if (progress < 0 || progress > 100) {
      setError('进度值异常');
    } else {
      setError(null);
    }
  }, [progress]);

  // 重置状态
  useEffect(() => {
    if (!visible) {
      setIsInitializing(true);
      setInitialProgress(0);
      setLocalProgress(0);
      hasInitialized.current = false;
    }
  }, [visible]);

  // 每2秒切换一次图标
  useEffect(() => {
    if (visible) {
      const emojis = ['📄', '📋', '📑', '📚', '📖', '📝', '🔍', '📊', '📈', '📉'];
      let index = 0;
      const interval = setInterval(() => {
        index = (index + 1) % emojis.length;
        setCurrentEmoji(emojis[index]);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [visible]);

  if (!visible) return null;

  const getStatusEmoji = () => {
    if (error) return '⚠️';
    return currentEmoji;
  };

  // 获取当前显示的进度值
  const getDisplayProgress = () => {
    if (isInitializing) {
      return initialProgress;
    }
    return localProgress;
  };

  // 获取当前状态描述
  const getStatusDescription = () => {
    if (error) return error;
    if (isInitializing) {
      return '正在初始化文档解析...';
    }
    return statusDesc || '请稍候，系统正在处理您的文档...';
  };

  return (
    <div className="document-parsing-overlay">
      {/* 炫酷的文档解析动画 */}
      <div className="animation-container">
        {/* 旋转的外圈 */}
        <div className="spinning-ring" />

        {/* 脉冲的中圈 */}
        <div className="pulsing-ring" />

        {/* 文档图标 */}
        <div className="bouncing-icon">{getStatusEmoji()}</div>

        {/* 扫描线效果 */}
        <div className="scanning-line" />
      </div>

      {/* 解析状态文字 */}
      {/* <div className="status-title">
        {error ? '解析出现异常' : '正在解析文档'}
      </div> */}

      {/* 进度描述 */}
      <div className="status-description">
        {error ? (
          <span style={{ color: '#ff4d4f' }}>{error}</span>
        ) : (
          getStatusDescription()
        )}
      </div>

      {/* 进度条 */}
      <div className="progress-container">
        <div 
          className="progress-bar" 
          style={{ 
            width: `${getDisplayProgress()}%`,
            backgroundColor: error ? '#ff4d4f' : undefined 
          }} 
        />
      </div>

      {/* 进度百分比 */}
      <div className="progress-text">
        {error ? '解析失败' : `${getDisplayProgress()}%`}
      </div>
    </div>
  );
};

export default DocumentParsingOverlay;
