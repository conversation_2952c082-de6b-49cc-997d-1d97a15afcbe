.container {
  display: flex;
  height: 90vh;
  // height: calc(100vh-30px);
  overflow: hidden;
  background: #f5f5f5;
}

.sideNav {
  width: 280px;
  padding: 16px;
  background: #fff;
  border-right: 1px solid #f0f0f0;
  overflow: auto;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 0 16px;
  padding: 0;
}

.toolbar {
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  margin-top: 16px;
}

.pdfContainer {
  flex: 1;
  // margin-top: 16px;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.container {
  height: calc(90vh - 64px); // 减去Steps组件的高度
  background: transparent;

  :global(.ant-layout-sider) {
    background: transparent;
  }
}

.content {
  background: transparent;
  transition: margin-right 0.3s ease;
}

.pdfViewer {
  width: 100%;
  height: 100%;
  border: none;
}

.rightPanelSlide {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.successDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #52c41a;
  display: inline-block;
}

.errorDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4d4f;
  display: inline-block;
}

.rightPanel {
  width: 50%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-left: 1px solid #f0f0f0;
  height: 100%;
  margin: 16px 16px 0 0;
}

.rightToolbar {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  // margin-bottom: 16px;
  text-align: right;

  :global(.ant-btn) {
    min-width: 120px;
  }
}

.resultCard {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  overflow: auto;
  box-shadow:0;
  border-top: 1px solid #f0f0f0;
  
  :global(.ant-card-body) {
    padding: 8px 16px;
  }
}
.content{
  margin: 0;
}
.toolbar{
  border-radius:0;
  margin-top: 0;
}
.rightPanel{
  margin: 0;
}
.rightToolbar{
  border-radius: 0;
}
.pdfContainer{
  border-radius: 0;
  border-top: 1px solid #f0f0f0;
  padding: 100px 0;
}
.resultCard{
  border-radius: 0;
}

.listItem {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.itemContent {
  width: 100%;
}

.itemHeader {
  display: flex;
  align-items: center;
  gap: 8px;
}

.itemText {
  flex: 1;
  margin-right: 8px;
}

.expandButton {
  padding: 4px;
  height: auto;
}

.itemDetails {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.detailSection {
  margin-bottom: 16px;

  h4 {
    color: #666;
    margin-bottom: 8px;
  }

  p {
    margin: 0;
    color: #333;
    line-height: 1.5;
  }
}

.detailActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.successDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #52c41a;
  flex-shrink: 0;
}

.errorDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4d4f;
  flex-shrink: 0;
}

.ant-pro-layout .ant-pro-layout-content{
  padding: 0;
}
.container___ulS8A{

}

// 自定义Steps组件样式
.timelineSteps {
  :global(.ant-steps-item-title) {
    font-size: 14px;
  }
  
  :global(.ant-steps-item-description) {
    font-size: 12px;
  }
}