import { Space, Spin, Steps } from 'antd';
import React from 'react';

interface ProcessStepsProps {
  currentStep: number;
  step2Percent: number;
  uploadStatus: string;
  statusDesc: string;
  detectStatus: string;
  showRightPanel: boolean;
  uploadedFileUrl?: string;
}

const ProcessSteps: React.FC<ProcessStepsProps> = ({
  currentStep,
  step2Percent,
  uploadStatus,
  statusDesc,
  detectStatus,
  showRightPanel,
  uploadedFileUrl,
}) => {
  // 动态生成时间轴数据
  const timelineItems = [
    {
      title: '上传PDF文件',
      description: uploadStatus,
    },
    {
      title: '解析PDF文件',
      description: uploadedFileUrl ? '解析成功' : statusDesc,
    },
    {
      title: '智能检测',
      description: detectStatus,
    },
  ];

  if (!showRightPanel) {
    return null;
  }

  return (
    <div style={{ width: '100%', padding: '16px 24px 10px' }}>
      <Steps
        current={currentStep}
        percent={currentStep === 1 ? step2Percent : undefined}
        items={timelineItems.map((item, index) => ({
          ...item,
          description:
            index === 1 && currentStep === 1 ? (
              <Space>
                <Spin size="small" />
                {item.description}
              </Space>
            ) : (
              item.description
            ),
        }))}
        style={{
          width: '100%',
          background: 'none',
          transition: 'all 0.3s ease-in-out',
        }}
      />
    </div>
  );
};

export default ProcessSteps;