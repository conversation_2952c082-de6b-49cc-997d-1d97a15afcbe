// 反馈按钮样式
.feedback-buttons-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

// 简单的图标弹跳动画 - 使用feedback前缀避免命名冲突
@keyframes feedbackIconBounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
}

.feedback-btn-accurate {
  border-color: #87d068;
  color: #87d068;
  
  &:hover {
    border-color: #73d13d;
    color: #73d13d;
  }
}

.feedback-btn-accurate-active {
  background-color: #87d068;
  border-color: #87d068;
  
  &:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }
  
  .anticon {
    animation: feedbackIconBounce 0.6s ease-in-out;
  }
}

.feedback-btn-inaccurate {
  border-color: #ffb366;
  color: #ffb366;
  
  &:hover {
    border-color: #ffa940;
    color: #ffa940;
  }
}

.feedback-btn-inaccurate-active {
  background-color: #ff7875;
  border-color: #ff7875;
  
  &:hover {
    background-color: #ff4d4f;
    border-color: #ff4d4f;
  }
  
  .anticon {
    animation: feedbackIconBounce 0.6s ease-in-out;
  }
}