import { DislikeTwoTone, LikeTwoTone } from '@ant-design/icons';
import { Button, message } from 'antd';
import React from 'react';
import { aiEvaluation } from '../../../services/evaluation';
import './index.less';

interface FeedbackButtonsProps {
  liked?: boolean;
  disliked?: boolean;
  evaluationId: string; // 评价对象的ID
  onStateChange?: (liked: boolean, disliked: boolean) => void; // 状态变化回调
  size?: 'small' | 'middle' | 'large';
}

const FeedbackButtons: React.FC<FeedbackButtonsProps> = ({
  liked = false,
  disliked = false,
  evaluationId,
  onStateChange,
  size = 'small',
}) => {
  // 处理点赞逻辑
  const handleLike = async () => {
    try {
      const newLiked = !liked;
      const isLike = newLiked ? 1 : 0;

      await aiEvaluation({ id: evaluationId, isLike });

      // 更新状态：如果点赞，则取消点踩
      const newDisliked = newLiked ? false : disliked;
      onStateChange?.(newLiked, newDisliked);

      const actionText = newLiked ? '"准确"操作成功' : '"取消准确"成功';
      message.success(actionText);
    } catch (error) {
      message.error('操作失败，请重试');
      console.error('准确操作失败:', error);
    }
  };

  // 处理点踩逻辑
  const handleDislike = async () => {
    try {
      const newDisliked = !disliked;
      const isLike = newDisliked ? 2 : 0;

      await aiEvaluation({ id: evaluationId, isLike });

      // 更新状态：如果点踩，则取消点赞
      const newLiked = newDisliked ? false : liked;
      onStateChange?.(newLiked, newDisliked);

      const actionText = newDisliked ? '"不准确"操作成功' : '"取消不准确"成功';
      message.success(actionText);
    } catch (error) {
      message.error('操作失败，请重试');
      console.error('不准确操作失败:', error);
    }
  };

  return (
    <div className="feedback-buttons-container">
      <Button
        type={liked ? 'primary' : 'default'}
        size={size}
        className={
          liked ? 'feedback-btn-accurate-active' : 'feedback-btn-accurate'
        }
        icon={<LikeTwoTone twoToneColor={liked ? '#87d068' : '#bfbfbf'} />}
        onClick={handleLike}
      >
        准确
      </Button>
      <Button
        type={disliked ? 'primary' : 'default'}
        size={size}
        danger={disliked}
        className={
          disliked
            ? 'feedback-btn-inaccurate-active'
            : 'feedback-btn-inaccurate'
        }
        icon={
          <DislikeTwoTone twoToneColor={disliked ? '#ffb366' : '#bfbfbf'} />
        }
        onClick={handleDislike}
      >
        不准确
      </Button>
    </div>
  );
};

export default FeedbackButtons;
