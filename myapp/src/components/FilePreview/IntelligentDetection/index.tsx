import {
  CheckCircleTwoTone,
  ExclamationCircleOutlined,
  SearchOutlined,
  ArrowLeftOutlined,
  KeyOutlined,
  LockOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Collapse,
  Layout,
  message,
  Space,
  Typography,
  Tooltip,
} from 'antd';
import React, { useState } from 'react';
import { aiApi } from '../../../services/api';
import DetectionStatusModal from '../DetectionStatusModal';
import FeedbackButtons from '../FeedbackButtons';
import ModelAuthModal from './ModelAuthModal';
import './index.less';

const { Title, Paragraph } = Typography;

interface ContentPair {
  id?: string;
  original: string;
  warning: string;
  liked?: boolean;
  disliked?: boolean;
}

interface ListItem {
  id: string;
  type: 'success' | 'error';
  text: string;
  originalContent?: string;
  contentPairs?: ContentPair[];
  references?: string[];
  pageNumber?: number;
  liked?: boolean;
  disliked?: boolean;
}

interface CollapseItem {
  key: string;
  label: React.ReactNode;
  extra?: React.ReactNode;
  children: React.ReactNode;
}

interface IntelligentDetectionProps {
  uploadResultId?: string;
  fileStatus: number | null;
  detectStatus: string;
  onDetectStatusChange: (status: string) => void;
  onContentJump?: (searchText: string) => void;
  onPageJump?: (pageNumber: number) => void;
  pdfUrl?: string;
  isJumping?: boolean;
  onBack?: () => void;
}

const IntelligentDetection: React.FC<IntelligentDetectionProps> = ({
  uploadResultId,
  fileStatus,
  onDetectStatusChange,
  onContentJump,
  pdfUrl,
  isJumping = false,
  onBack,
}) => {
  const [detecting, setDetecting] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<ListItem[]>([]);
  const [showStatusModal, setShowStatusModal] = useState<boolean>(false);
  const [showAuthModal, setShowAuthModal] = useState<boolean>(false);
  const [isAuthorized, setIsAuthorized] = useState<boolean>(false);

  // 生成唯一ID的工具函数
  // const generateUniqueId = (prefix?: string) => {
  //   const timestamp = Date.now();
  //   const random = Math.random().toString(36).substr(2, 9);
  //   return prefix
  //     ? `${prefix}-${timestamp}-${random}`
  //     : `${timestamp}-${random}`;
  // };

  const fetchFinalResults = async () => {
    const pollResults = async (): Promise<boolean> => {
      try {
        if (!uploadResultId) {
          throw new Error('未获取到文件ID');
        }
        const result = await aiApi.aiCheck(uploadResultId);
        if (result.success) {
          // 检查数据是否准备完成
          if (!result.result || result.result.length === 0) {
            // 数据还没准备好，继续轮询
            console.log('数据还没准备好，继续轮询...');
            return false;
          }

          // 检查是否所有规则都已完成处理
          const hasIncompleteRules = result.result.some((rule: any) => {
            // 如果规则状态不是最终状态，或者数据还在处理中，则继续轮询
            return (
              rule.status === undefined ||
              rule.status === null ||
              (rule.status === 0 && (!rule.data || rule.data.length === 0))
            );
          });

          if (hasIncompleteRules) {
            console.log('检测还在进行中，继续轮询...');
            return false;
          }

          const newDataSource: ListItem[] = [];
          result.result.forEach((rule: any) => {
            if (rule.status === 0 && rule.data && rule.data.length > 0) {
              // 处理错误的预警内容
              const contentPairs = rule.data.map((item: any) => ({
                id: item.id,
                original: item.originalContent,
                warning: item.warningContent,
                liked: item.isLike === 1,
                disliked: item.isLike === 2,
              }));

              newDataSource.push({
                id: rule.ruleId,
                type: 'error',
                text: rule.ruleName,
                contentPairs,
                references: ['《招标投标领域公平竞争审查规则》'],
                // 使用第一个contentPair的点赞状态作为规则的状态
                liked: contentPairs[0]?.liked || false,
                disliked: contentPairs[0]?.disliked || false,
              });
            } else if (rule.status === 1) {
              // 处理正确的预警内容
              const contentPairs =
                rule.data?.map((item: any) => ({
                  id: item.id,
                  original: item.originalContent,
                  warning: item.warningContent,
                  liked: item.isLike === 1,
                  disliked: item.isLike === 2,
                })) || [];

              newDataSource.push({
                id: rule.ruleId,
                type: 'success',
                text: rule.ruleName,
                contentPairs,
                references: ['《招标投标领域公平竞争审查规则》'],
                // 使用第一个contentPair的点赞状态
                liked: contentPairs[0]?.liked || false,
                disliked: contentPairs[0]?.disliked || false,
              });
            }
          });

          console.log('检测完成，获取到数据:', newDataSource.length, '条');
          setDataSource(newDataSource);
          onDetectStatusChange('检测完成');
          return true; // 数据获取完成
        } else {
          throw new Error('检测结果异常');
        }
      } catch (error) {
        console.error('获取结果失败:', error);
        throw error;
      }
    };

    try {
      // 开始轮询
      const maxAttempts = 3; // 最多轮询3次
      const pollInterval = 3000; // 每3秒轮询一次

      for (let attempt = 0; attempt < maxAttempts; attempt++) {
        const isCompleted = await pollResults();
        if (isCompleted) {
          return; // 成功获取数据，退出轮询
        }

        // 等待下次轮询
        if (attempt < maxAttempts - 1) {
          await new Promise<void>((resolve) => {
            setTimeout(() => resolve(), pollInterval);
          });
        }
      }

      // 轮询超时
      throw new Error('获取检测结果超时');
    } catch (error) {
      message.error('获取检测结果失败');
      console.error('获取结果失败:', error);
    } finally {
      setDetecting(false);
    }
  };

  const handleDetection = async () => {
    if (!uploadResultId) {
      message.error('请先上传文件');
      return;
    }

    // 先检查是否已有预警内容数据
    // if (dataSource.length > 0) {
    //   console.log('预警内容数据已存在，无需重新检测');
    //   message.info('检测结果已存在，无需重复检测');
    //   setDetecting(false); // 确保detecting状态为false
    //   return;
    // }

    setDetecting(true);
    setDataSource([]);
    onDetectStatusChange('检测中...');
    setShowStatusModal(true);

    // 同时请求ai-check接口
    try {
      const result = await aiApi.aiCheck(uploadResultId);
      if (!result.success) {
        throw new Error(result.message || '检测失败');
      }

      // 请求成功后，立即开始轮询获取结果
      console.log('ai-check请求成功，开始轮询获取结果...');

      // 不再在这里调用fetchFinalResults，让DetectionStatusModal组件处理状态检测
      // 状态检测由DetectionStatusModal组件通过ai-check-status接口处理
    } catch (error) {
      console.error('检测失败:', error);
      setShowStatusModal(false);
      setDetecting(false);
      onDetectStatusChange('检测失败');
      message.error('检测失败，请重试');
    }
  };

  const handleStatusComplete = async () => {
    setShowStatusModal(false);
    // 只有在没有数据的情况下才调用fetchFinalResults
    if (dataSource.length === 0) {
      await fetchFinalResults();
    } else {
      // 如果已有数据，直接设置detecting为false
      setDetecting(false);
    }
  };

  const handleStatusError = (error: string) => {
    setShowStatusModal(false);
    setDetecting(false);
    onDetectStatusChange('检测失败');
    message.error(error);
  };

  const handleContentClick = (pair: ContentPair) => {
    console.log('点击原文事件触发，原文内容:', pair.original);
    if (pdfUrl && pair.original && onContentJump) {
      const searchText =
        pair.original.length > 8
          ? pair.original.substring(0, 8)
          : pair.original.length > 6
          ? pair.original.substring(0, 6)
          : pair.original;
      console.log('截取后的搜索关键词:', searchText);
      onContentJump(searchText);
    }
  };

  // 处理item级别的状态更新
  const handleItemStateChange = (
    itemId: string,
    liked: boolean,
    disliked: boolean,
  ) => {
    setDataSource((prev) =>
      prev.map((i) => {
        if (i.id === itemId) {
          return {
            ...i,
            liked,
            disliked,
          };
        }
        return i;
      }),
    );
  };

  // 处理contentPair级别的状态更新
  const handleContentPairStateChange = (
    itemId: string,
    pairIndex: number,
    liked: boolean,
    disliked: boolean,
  ) => {
    setDataSource((prev) =>
      prev.map((i) => {
        if (
          i.id === itemId &&
          i.contentPairs &&
          pairIndex < i.contentPairs.length
        ) {
          return {
            ...i,
            contentPairs: i.contentPairs.map((p, idx) =>
              idx === pairIndex
                ? {
                    ...p,
                    liked,
                    disliked,
                  }
                : p,
            ),
          };
        }
        return i;
      }),
    );
  };

  // 获取评价ID的辅助函数
  const getItemEvaluationId = (item: ListItem): string => {
    // 对于规则项，使用其contentPairs中第一个的id
    return item.contentPairs?.[0]?.id || item.id;
  };

  const getContentPairEvaluationId = (
    item: ListItem,
    pairIndex: number,
  ): string => {
    return item.contentPairs?.[pairIndex]?.id || `${item.id}-${pairIndex}`;
  };

  const collapseItems: CollapseItem[] = dataSource.map((item) => ({
    key: item.id,
    label: (
      <Space align="center">
        {item.type === 'success' ? (
          <CheckCircleTwoTone twoToneColor="#52c41a" />
        ) : (
          <ExclamationCircleOutlined style={{ color: '#FA8C16' }} />
        )}
        <span>{item.text}</span>
      </Space>
    ),
    children:
      item.type === 'success' ? (
        <Card
          variant="borderless"
          styles={{ body: { background: '#f6ffed', padding: '24px' } }}
        >
          <div className="success-content">
            <CheckCircleTwoTone
              className="success-icon"
              twoToneColor="#52c41a"
            />
            <div className="success-text">🎉 此条审核通过，暂未发现异常</div>
            <div style={{ marginTop: 16, fontSize: 12, color: '#666' }}>
              符合《招标投标领域公平竞争审查规则》相关要求
            </div>
          </div>
          <Space
            style={{
              justifyContent: 'center',
              width: '100%',
              paddingTop: 16,
              borderTop: '1px solid #d9f7be',
              marginTop: 16,
            }}
          >
            <span style={{ fontSize: 12, color: '#666', marginRight: 8 }}>
              评价此结果：
            </span>
            <FeedbackButtons
              liked={item.liked}
              disliked={item.disliked}
              evaluationId={getItemEvaluationId(item)}
              onStateChange={(liked, disliked) =>
                handleItemStateChange(item.id, liked, disliked)
              }
            />
          </Space>
        </Card>
      ) : (
        <Card
          variant="borderless"
          styles={{
            body: { background: '#fafafa', padding: '16px 24px 10px' },
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }} size={16}>
            {item.contentPairs
              ? item.contentPairs.map((pair, index) => (
                  <div key={`pair-${index}`} className="content-pair-container">
                    <div className="original-content-section">
                      <Title level={5} className="section-title">
                        <span className="title-icon">📄</span>
                        原文内容：
                      </Title>
                      <div className="content-wrapper original-wrapper">
                        <Paragraph
                          onClick={
                            isJumping
                              ? undefined
                              : () => handleContentClick(pair)
                          }
                          className={`clickable-content ${
                            isJumping ? 'disabled' : ''
                          }`}
                          style={{
                            cursor: isJumping ? 'not-allowed' : 'pointer',
                            opacity: isJumping ? 0.6 : 1,
                          }}
                        >
                          {pair.original}
                        </Paragraph>
                        <div className="content-actions">
                          <Button
                            type="text"
                            size="small"
                            icon={<SearchOutlined />}
                            className="jump-button"
                            onClick={() => handleContentClick(pair)}
                            disabled={isJumping}
                            loading={isJumping}
                          >
                            {isJumping ? '定位中...' : '定位原文'}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="warning-content-section">
                      <Title level={5} className="section-title warning-title">
                        <span className="title-icon">⚠️</span>
                        预警内容：
                      </Title>
                      <div className="content-wrapper warning-wrapper">
                        <Paragraph className="warning-content enhanced">
                          {pair.warning}
                        </Paragraph>
                      </div>
                    </div>

                    <div className="feedback-section">
                      <Space className="feedback-buttons">
                        <span className="feedback-label">此预警是否准确？</span>
                        <FeedbackButtons
                          liked={pair.liked}
                          disliked={pair.disliked}
                          evaluationId={getContentPairEvaluationId(item, index)}
                          onStateChange={(liked, disliked) =>
                            handleContentPairStateChange(
                              item.id,
                              index,
                              liked,
                              disliked,
                            )
                          }
                        />
                      </Space>
                    </div>
                  </div>
                ))
              : null}
          </Space>
        </Card>
      ),
  }));

  const handleBack = () => {
    // 刷新当前页面
    window.location.reload();
  };

  const handleModelAuth = () => {
    setShowAuthModal(true);
  };

  const handleAuthModalClose = () => {
    setShowAuthModal(false);
  };

  const handleAuth = () => {
    // TODO: 实现授权逻辑
    // message.success('授权成功');
    setShowAuthModal(false);
    setIsAuthorized(true);
  };

  const renderContent = () => {
    if (!isAuthorized) {
      return (
        <div className="empty-state">
          <div style={{ fontSize: 16 }}>请先进行模型授权</div>
        </div>
      );
    }

    if (showStatusModal) {
      return (
        <div className="empty-state">
          <div style={{ fontSize: 16 }}>等待智能检测结果 ⌛️</div>
        </div>
      );
    }

    if (dataSource.length > 0) {
      return (
        <Collapse
          accordion
          bordered={false}
          expandIconPosition="end"
          style={{ background: 'transparent' }}
          items={collapseItems}
        />
      );
    }

    return (
      <div className="empty-state">
        <div style={{ fontSize: 16 }}>试试我们的智能检测吧 🚀</div>
      </div>
    );
  };

  return (
    <Layout.Sider className="intelligent-detection-sider">
      <Card styles={{ body: { padding: 16 } }} className="detection-actions-card">
        <div className="detection-actions">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
          >
            返回
          </Button>
          <div className="right-actions">
            {isAuthorized && (
              <>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  disabled={!(uploadResultId && isAuthorized)}
                  loading={detecting}
                  onClick={handleDetection}
                >
                  智能检测
                </Button>
                <Tooltip title="模型授权" placement="bottom">
                  <Button
                    icon={<KeyOutlined />}
                    onClick={handleModelAuth}
                  >
                    {/* 模型授权 */}
                  </Button>
                </Tooltip>
              </>
            )}
          </div>
        </div>
      </Card>

      {!isAuthorized ? (
        <div className="auth-notice-wrapper">
          <div className="auth-notice-card">
            <div className="auth-notice-icon">
              <LockOutlined style={{ width: 60, height: 60 }} />
            </div>
            <div className="auth-notice-title">
              模型未授权
            </div>
            <div className="auth-notice-content">
              请先进行模型授权，授权后即可使用智能检测功能
            </div>
            <Button 
              type="primary" 
              icon={<KeyOutlined />}
              onClick={handleModelAuth}
              className="auth-notice-button"
            >
              立即授权
            </Button>
          </div>
        </div>
      ) : (
        <Card title="预警内容" className="detection-results-card">
          {renderContent()}
        </Card>
      )}

      <DetectionStatusModal
        open={showStatusModal}
        uploadResultId={uploadResultId}
        onComplete={handleStatusComplete}
        onError={handleStatusError}
      />

      <ModelAuthModal
        open={showAuthModal}
        onClose={handleAuthModalClose}
        onAuth={handleAuth}
        onAuthChange={(authorized) => setIsAuthorized(authorized)}
      />
    </Layout.Sider>
  );
};

export default IntelligentDetection;
