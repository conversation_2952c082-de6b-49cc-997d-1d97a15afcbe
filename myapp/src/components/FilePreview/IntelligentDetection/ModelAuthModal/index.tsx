import React, { useEffect, useState } from 'react';
import { Modal, Button, message, Space } from 'antd';
import { aiApi } from '@/services/api';
import './index.less';

interface ModelAuthModalProps {
  open: boolean;
  onClose: () => void;
  onAuth: () => void;
  onAuthChange?: (authorized: boolean) => void;
}

interface AppInfo {
  id: string;
  name: string;
  icon: string;
}

const LOCAL_STORAGE_KEY = 'model_auth_status';
const SELECTED_APP_KEY = 'selected_app_id';

const ModelAuthModal: React.FC<ModelAuthModalProps> = ({
  open,
  onClose,
  onAuth,
  onAuthChange,
}) => {
  const [isAuthorized, setIsAuthorized] = useState<boolean>(false);
  const [appList, setAppList] = useState<AppInfo[]>([]);
  const [selectedAppId, setSelectedAppId] = useState<string>('');

  useEffect(() => {
    if (open) {
      aiApi.getAuthAppList().then(({ success, result }) => {
        if (success && result) {
          setAppList(result);
          const savedAppId = localStorage.getItem(SELECTED_APP_KEY);
          if (savedAppId && result.some((app: AppInfo) => app.id === savedAppId)) {
            setSelectedAppId(savedAppId);
          } else if (result.length > 0) {
            setSelectedAppId(result[0].id);
          }
        }
      });
    }
  }, [open]);

  useEffect(() => {
    const storedAuthStatus = localStorage.getItem(LOCAL_STORAGE_KEY);
    const authStatus = storedAuthStatus === 'true';
    setIsAuthorized(authStatus);
    onAuthChange?.(authStatus);
  }, [onAuthChange]);

  const handleAuthClick = () => {
    if (!selectedAppId) {
      message.error('请选择要授权的应用');
      return;
    }

    const newAuthStatus = !isAuthorized;
    localStorage.setItem(LOCAL_STORAGE_KEY, String(newAuthStatus));
    localStorage.setItem(SELECTED_APP_KEY, selectedAppId);
    setIsAuthorized(newAuthStatus);
    onAuthChange?.(newAuthStatus);
    
    if (newAuthStatus) {
      message.success('授权成功');
    } else {
      message.success('取消授权成功');
    }
    
    onAuth();
  };

  return (
    <Modal
      title="检测模型授权"
      open={open}
      onCancel={onClose}
      footer={[
        <Button key="auth" type="primary" onClick={handleAuthClick}>
          {isAuthorized ? '取消授权' : '授权'}
        </Button>
      ]}
      width={600}
      closeIcon={<span className="modal-close">×</span>}
    >
      <div className="model-auth-content">
        <div className="auth-notice">
          <span className="notice-text">
            {isAuthorized 
              ? '* 您已授权规则引擎平台的模型应用进行文档检测'
              : '* 您需要授权规则引擎平台的模型应用才能进行文档检测'}
          </span>
        </div>
        <div className="app-list">
          <Space align="start" wrap>
            {appList.map(app => (
              <div
                key={app.id}
                className={`app-item ${selectedAppId === app.id ? 'selected' : ''}`}
                onClick={() => setSelectedAppId(app.id)}
              >
                <div className="app-content">
                  <div className="logo-placeholder">
                    {app.icon ? (
                      <img src={app.icon} alt={app.name} />
                    ) : (
                      <span>{app.name.charAt(0)}</span>
                    )}
                  </div>
                  <div className="app-name">{app.name}</div>
                </div>
              </div>
            ))}
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default ModelAuthModal; 