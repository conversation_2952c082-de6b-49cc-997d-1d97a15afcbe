.model-auth-content {
  padding: 24px 0;

  .auth-notice {
    margin-bottom: 24px;
    
    .notice-text {
      color: #ff4d4f;
      font-size: 14px;
    }
  }

  .app-list {
    width: 100%;

    .app-item {
      margin: 8px;
      cursor: pointer;
      
      .app-content {
        text-align: center;
        padding: 16px;
        background: #f5f5f5;
        border: 1px solid transparent;
        border-radius: 8px;
        width: 120px;
        transition: all 0.3s ease;

        &:hover {
          background: #e6f7ff;
          border-color: #91d5ff;
        }

        .logo-placeholder {
          width: 60px;
          height: 60px;
          background-color: #fff;
          border: 1px dashed #d9d9d9;
          margin: 0 auto 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          border-radius: 4px;
          
          span {
            color: #999;
            font-size: 20px;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .app-name {
          color: #333;
          font-size: 14px;
          margin: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      // 选中状态样式
      &.selected {
        .app-content {
          background: #e6f7ff;
          border-color: #1890ff;
        }
      }
    }
  }
}

.modal-close {
  font-size: 18px;
  font-weight: normal;
  color: #999;
  
  &:hover {
    color: #666;
  }
}

:global {
  .ant-modal-header {
    margin-bottom: 0;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-content {
    padding: 0;
  }

  .ant-modal-body {
    padding: 0 24px;
  }

  .ant-modal-footer {
    margin-top: 0;
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;

    .ant-btn {
      min-width: 120px;
    }
  }
} 