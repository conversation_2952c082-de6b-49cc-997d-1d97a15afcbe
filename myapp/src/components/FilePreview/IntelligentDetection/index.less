// 智能检测组件样式
.intelligent-detection-sider {
  width: 50% !important;
  max-width: 50% !important;
  min-width: 50% !important;
  flex: 0 0 50% !important;
  background: #fff;
  // border-left: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.detection-actions-card {
  :global(.ant-card-body) {
    padding: 16px !important;
  }
}

.detection-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;

  button + button,
  button + .ant-tooltip-open {
    margin-left: 8px;
  }
}

.auth-notice-wrapper {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 24px;
  background-color: #fafafa;
  height: 100%;
  position: relative;
}

.auth-notice-card {
  background: #fff;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  width: 100%;
  max-width: 400px;
  transition: all 0.3s ease;
  margin-top: 0vh;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.auth-notice-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;

  .anticon {
    background: #e6f7ff;
    padding: 16px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}

.auth-notice-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 12px;
}

.auth-notice-content {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 24px;
  line-height: 1.6;
}

.auth-notice-button {
  // min-width: 120px;
  // height: 36px;
  font-size: 12px;
  border-radius: 4px;
}

.detection-results-card {
  margin-top: 1px;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px 16px;
  height: calc(100% - 57px);
  overflow: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  padding: 24px;
}

.clickable-content {
  cursor: pointer;
  color: #333;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
  margin-bottom: 8px;
  padding: 8px;

  &:hover {
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}

.warning-content {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
  color: #666;
  line-height: 1.6;
  word-break: break-word;
}

.like-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 8px;
}

// 添加新的样式类
.content-pair-container {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background: #fafafa;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.original-content-section {
  margin-bottom: 16px;
}

.warning-content-section {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px !important;
  font-weight: 600;

  .title-icon {
    margin-right: 6px;
    font-size: 14px;
  }

  &.warning-title {
    color: #fa8c16;
  }
}

.content-wrapper {
  position: relative;
  border-radius: 6px;
  padding: 12px;

  &.original-wrapper {
    background: #e6f7ff;
    border-left: 4px solid #1890ff;
  }

  &.warning-wrapper {
    background: #fff7e6;
    border-left: 4px solid #fa8c16;
  }
}

.clickable-content {
  margin-bottom: 0 !important;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(24, 144, 255, 0.1);
    transform: translateY(-1px);
  }
}

.warning-content.enhanced {
  margin-bottom: 0 !important;
  // font-weight: 500;
  color: #d46b08;
}

.content-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.content-wrapper:hover .content-actions {
  opacity: 1;
}

.jump-button {
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
}

.feedback-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
  display: flex;
  justify-content: center;
}

.feedback-buttons {
  align-items: center;
}

.feedback-label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

// 反馈按钮样式优化
.feedback-btn-accurate {
  border-color: #d9d9d9 !important;
  color: #8c8c8c !important;
  background: #fafafa !important;

  &:hover {
    border-color: #bfbfbf !important;
    color: #595959 !important;
    background: #f0f0f0 !important;
  }
}

.feedback-btn-accurate-active {
  background: #f6ffed !important;
  border-color: #b7eb8f !important;
  color: #52c41a !important;

  &:hover {
    background: #f0f9ff !important;
    border-color: #95de64 !important;
    color: #389e0d !important;
  }
}

.feedback-btn-inaccurate {
  border-color: #d9d9d9 !important;
  color: #8c8c8c !important;
  background: #fafafa !important;

  &:hover {
    border-color: #bfbfbf !important;
    color: #595959 !important;
    background: #f0f0f0 !important;
  }
}

.feedback-btn-inaccurate-active {
  background: #fff2e8 !important;
  border-color: #ffbb96 !important;
  color: #fa8c16 !important;

  &:hover {
    background: #ffe7d3 !important;
    border-color: #ff9c6e !important;
    color: #d46b08 !important;
  }
}

// 优化折叠面板样式
.ant-collapse {
  .ant-collapse-item {
    border-radius: 8px !important;
    margin-bottom: 8px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-collapse-header {
    padding: 12px 16px !important;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    &:hover {
      background: #e9ecef;
    }
  }

  .ant-collapse-content {
    border-top: none;
  }
}

// 成功状态的特殊样式
.success-content {
  text-align: center;
  padding: 24px;

  .success-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }

  .success-text {
    font-size: 16px;
    color: #52c41a;
    font-weight: 500;
  }
}
