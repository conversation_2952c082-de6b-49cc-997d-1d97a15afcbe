import { Layout, Row, Typography } from 'antd';
import React from 'react';
import styles from './Guide.less';

interface Props {
  // 如果不需要 name 属性，移除这一行
}

const Guide: React.FC<Props> = (props) => {
  // 如果不需要 name，移除这一行
  return (
    <Layout>
      <Row>
        <Typography.Title level={3} className={styles.title}>
          智能检测系统
        </Typography.Title>
      </Row>
    </Layout>
  );
};

export default Guide;
