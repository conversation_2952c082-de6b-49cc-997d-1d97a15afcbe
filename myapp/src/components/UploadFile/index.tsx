import { InboxOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { message, Upload } from 'antd';
import { fileApi } from '../../services/api';
import styles from './index.less';

const { Dragger } = Upload;

export interface UploadResult {
  id: string;
  originFileName: string;
  fileMd5: string;
  fileSize: number;
  uploadedFileName: string;
  uploadedFileUrl: string;
  createTime: string;
}

export interface UploadFileProps {
  onUploadSuccess?: (file: File, result: UploadResult) => void;
  onUploadError?: (error: string) => void;
}

const UploadFile: React.FC<UploadFileProps> = ({
  onUploadSuccess,
  onUploadError,
}) => {
  console.log('UploadFile组件渲染');
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.pdf',
    onChange: (info) => {
      console.log('onChange事件触发:', info.file.status);
    },
    beforeUpload: (file) => {
      console.log('beforeUpload触发，文件:', file.name);
      if (file.size > 100 * 1024 * 1024) {
        const msg = '文件大小不能超过10MB';
        message.error(msg);
        onUploadError?.(msg);
        return Upload.LIST_IGNORE;
      }
      if (file.type !== 'application/pdf') {
        const msg = '仅支持PDF文件';
        message.error(msg);
        onUploadError?.(msg);
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    customRequest: async ({ file, onSuccess, onError }) => {
      console.log('customRequest被调用，文件:', file);
      if (!file) {
        console.error('未接收到文件');
        return;
      }

      try {
        const fileObj = file as File;
        console.log('开始上传文件:', fileObj.name);
        const formData = new FormData();
        formData.append('file', fileObj);
        const formDataEntries = Array.from(formData.entries());
        console.log('FormData详细内容:');
        formDataEntries.forEach(([key, value]) => {
          if (value instanceof File) {
            console.log(
              `- ${key}: File(${value.name}, ${value.size} bytes, ${value.type})`,
            );
          } else {
            console.log(`- ${key}:`, value);
          }
        });

        console.log('准备发送上传请求...');
        try {
          const data = await fileApi.upload(formData);
          console.log('上传请求已发出，响应数据:', data);
          onUploadSuccess?.(file as File, data.result);
          onSuccess?.(data);
        } catch (error) {
          console.error('fetch请求错误:', error);
          throw error;
        }
      } catch (error: unknown) {
        console.error('上传错误详情:', error);
        const msg = '文件上传失败';
        message.error(msg);
        onUploadError?.(msg);
        onError?.(error as Error);
      }
    },
  };

  return (
    <div className={styles.container}>
      <Dragger {...uploadProps}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽PDF文件到此区域</p>
        <p className="ant-upload-hint">支持单个PDF文件，最大10MB</p>
      </Dragger>
    </div>
  );
};

export default UploadFile;
