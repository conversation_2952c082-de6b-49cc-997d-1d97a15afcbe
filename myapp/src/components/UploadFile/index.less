.container {
  width: 100%;
  padding: 20px; // 增加内边距
  background-color: #fff; // 设置背景颜色
  border-radius: 8px; // 添加圆角
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09); // 添加阴影效果
}

.ant-upload-drag {
  border: 2px dashed #d9d9d9 !important; // 设置虚线边框
  background-color: #fafafa !important; // 设置背景颜色
  padding: 40px 20px !important; // 调整内边距
  text-align: center; // 文本居中

  .ant-upload-drag-icon {
    margin-bottom: 20px; // 调整图标下边距
    .anticon {
      font-size: 48px; // 调整图标大小
      color: #1890ff; // 设置图标颜色
    }
  }

  .ant-upload-text {
    font-size: 16px; // 调整主文本字体大小
    color: rgba(0, 0, 0, 0.85); // 设置主文本颜色
    margin-bottom: 8px; // 调整主文本下边距
  }

  .ant-upload-hint {
    font-size: 14px; // 调整提示文本字体大小
    color: rgba(0, 0, 0, 0.45); // 设置提示文本颜色
  }
}
