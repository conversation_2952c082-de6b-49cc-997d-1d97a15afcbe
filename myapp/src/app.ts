// 运行时配置
import { history } from '@umijs/max';

// 全局路由守卫
export function render(oldRender: () => void) {
  // 检查当前路径
  const { pathname } = history.location;
  
  // 根路径重定向
  if (pathname === '/') {
    // 双重保障：同时使用history和location
    history.replace('/home');
    if (typeof window !== 'undefined' && window.location.pathname === '/') {
      window.location.replace('/home');
    }
    return;
  }

  oldRender();
}

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
// export async function getInitialState(): Promise<{
//   name: string;
//   isLogin: boolean;
// }> {
//   // 这里可以添加实际的登录状态检查逻辑
//   return { 
//     name: '智能检测',
//     isLogin: true // 默认设为true确保路由正常工作
//   };
// }

export const layout = () => {
  return {
    // logo: 'https://img.alicdn.com/tfs/TB1YHEpwUT1gK0jSZFhXXaAtVXa-28-27.svg',
    logo: '/poc-logo.jpg',
    menu: {
      locale: false,
    },
  };
};
