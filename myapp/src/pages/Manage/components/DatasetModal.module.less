.datasetModalContent {
  padding: 24px 0 8px 0;
  .notice {
    color: #ff4d4f;
    font-size: 14px;
    margin-bottom: 24px;
    // padding-left: 24px;
  }

  .datasetList {
    display: flex;
    flex-wrap: wrap;
    gap: 24px 24px;
    justify-content: flex-start;
    align-items: flex-start;
    min-height: 160px;
    // padding: 0 24px 8px 24px;
  }

  .datasetCard {
    width: 120px;
    height: 120px;
    background: #f5f7fa;
    border: 1.5px solid #e5e6eb;
    border-radius: 10px;
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: none;
    padding: 0;
    overflow: hidden;

    &:hover,
    &.selected {
      border-color: #1890ff;
      background: #e6f7ff;
      box-shadow: 0 0 0 2px #bae7ff;
    }

    .datasetLogo {
      width: 36px;
      height: 36px;
      object-fit: contain;
      display: block;
      margin: 0 auto 12px auto;
    }

    .datasetName {
      color: #333;
      font-size: 14px;
      text-align: center;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0 8px;
    }
  }
} 