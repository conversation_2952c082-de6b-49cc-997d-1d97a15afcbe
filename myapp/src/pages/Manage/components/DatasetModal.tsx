import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, message } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { ruleApi } from '../../../services/api';
import styles from './DatasetModal.module.less';

export interface DatasetItem {
  id: string;
  name: string;
  logo: string;
}

interface DatasetModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (dataset: DatasetItem | null) => void;
  initialSelectedId?: string;
  isViewMode: boolean;
}

const DatasetModal: React.FC<DatasetModalProps> = ({ visible, onCancel, onConfirm, initialSelectedId, isViewMode }) => {
  const [datasetList, setDatasetList] = useState<DatasetItem[]>([]);
  const [datasetLoading, setDatasetLoading] = useState(false);
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(initialSelectedId || '');
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchDatasetList();
      setSelectedDatasetId(initialSelectedId || '');
    }
  }, [visible, initialSelectedId]);

  const fetchDatasetList = async () => {
    setDatasetLoading(true);
    try {
      const response = await ruleApi.fetchDataset();
      if (response.success) {
        setDatasetList(response.result.datasetList);
      }
    } catch (error) {
      message.error('获取数据集列表失败');
    } finally {
      setDatasetLoading(false);
    }
  };

  const handleDatasetSelect = (datasetId: string) => {
    setSelectedDatasetId(datasetId);
  };

  const handleConfirm = async () => {
    if (!selectedDatasetId) {
      message.warning('请选择一个数据集');
      return;
    }
    setConfirmLoading(true);
    try {
      // 调用 choose-dataset 接口
      const res = await ruleApi.chooseDataset({ id: selectedDatasetId });
      if (res.success) {
        const selected = datasetList.find(ds => ds.id === selectedDatasetId) || null;
        onConfirm(selected);
        message.success('选择数据集成功');
      } else {
        message.error(res.message || '选择数据集失败');
      }
    } catch (e) {
      message.error('选择数据集失败');
    } finally {
      setConfirmLoading(false);
    }
  };

  return (
    <Modal
      title={isViewMode
        ? '查看检测项'
        : selectedDatasetId
        ? '修改检测项'
        : '新增检测项'}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel} disabled={confirmLoading}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          onClick={handleConfirm}
          disabled={!selectedDatasetId}
          loading={confirmLoading}
        >
          确认
        </Button>,
      ]}
      width={520}
      closeIcon={<CloseOutlined />}
    >
      <div className={styles.datasetModalContent}>
        <div className={styles.notice}>
          * 您可以从高质量数据集平台中选择对应官方条例数据快速生成检测项目
        </div>
        <Spin spinning={datasetLoading}>
          <div className={styles.datasetList}>
            {datasetList.map((dataset) => (
              <div
                key={dataset.id}
                className={`${styles.datasetCard} ${selectedDatasetId === dataset.id ? styles.selected : ''}`}
                onClick={() => handleDatasetSelect(dataset.id)}
              >
                <img src={dataset.logo} alt={dataset.name} className={styles.datasetLogo} />
                <div className={styles.datasetName}>{dataset.name}</div>
              </div>
            ))}
          </div>
        </Spin>
      </div>
    </Modal>
  );
};

export default DatasetModal; 