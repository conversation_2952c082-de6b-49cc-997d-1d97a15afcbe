import {
  CaretDownOutlined,
  CaretUpOutlined,
  DatabaseOutlined,
  ExperimentOutlined,
  ImportOutlined,
  LoadingOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import { ruleApi } from '../../services/api';
import DatasetModal from './components/DatasetModal';
import styles from './index.less';

interface RuleItem {
  id: string;
  index: number;
  ruleName: string;
  keywords: string;
  positiveExample: string;
  negativeExample: string;
  ruleDesc: string;
  status: number;
  statusCnDesc: string;
  modifyTime: string;
  datasets: string[];
}

interface DatasetItem {
  id: string;
  name: string;
  logo: string;
}

const Manage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [isDatasetExpanded, setIsDatasetExpanded] = useState(false);
  const [data, setData] = useState<RuleItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [publishLoading, setPublishLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [selectedDatasets, setSelectedDatasets] = useState<string[]>([]);
  const [originalValues, setOriginalValues] = useState<any>(null);
  const [isDatasetModalOpen, setIsDatasetModalOpen] = useState(false);
  const [datasetList, setDatasetList] = useState<DatasetItem[]>([]);
  const [datasetLoading, setDatasetLoading] = useState(false);
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>('');
  const [ruleAutoLoading, setRuleAutoLoading] = useState(false);
  const [showRuleAutoModal, setShowRuleAutoModal] = useState(false);

  const fetchData = async (keywords = '') => {
    setLoading(true);
    try {
      const response = await ruleApi.getPage({
        page: pagination.current,
        pageSize: pagination.pageSize,
        keywords,
      });

      if (response.success) {
        setData(response.result.records);
        setPagination({
          ...pagination,
          total: response.result.total,
        });
      }
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchPublish = async (status: number, idList?: React.Key[]) => {
    const ids = idList || selectedRowKeys;
    if (ids.length === 0) {
      message.warning('请至少选择一条规则');
      return;
    }

    setPublishLoading(true);
    try {
      const response = await ruleApi.batchPublish({
        idList: ids,
        status,
      });

      if (response.success) {
        message.success(status === 1 ? '发布成功' : '取消发布成功');
        fetchData();
        if (!idList) setSelectedRowKeys([]);
      }
    } catch (error) {
      message.error('操作失败');
    } finally {
      setPublishLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [pagination.current]);
  const [isViewMode, setIsViewMode] = useState(false);
  const columns: ColumnsType<RuleItem> = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      align: 'center',
    },
    {
      title: '检测项目',
      dataIndex: 'ruleName',
      key: 'ruleName',
      ellipsis: true,
      render: (text) => (
        <div style={{ maxWidth: 150, whiteSpace: 'nowrap' }}>{text}</div>
      ),
    },
    // {
    //   title: '关联数据集',
    //   dataIndex: 'datasets',
    //   key: 'datasets',
    //   ellipsis: true,
    //   render: (datasets: string[]) => (
    //     <div style={{ maxWidth: 150, whiteSpace: 'nowrap' }}>
    //       <Space>
    //         {datasets.map((dataset) => (
    //           <Tag key={dataset}>
    //             {dataset === 'market' ? '市场准入负面数据集' : '资质数据集'}
    //           </Tag>
    //         ))}
    //       </Space>
    //     </div>
    //   ),
    // },
    {
      title: '命中关键词组',
      dataIndex: 'keywords',
      key: 'keywords',
      ellipsis: true,
      render: (text) => (
        <div style={{ maxWidth: 150, whiteSpace: 'nowrap' }}>{text}</div>
      ),
    },
    {
      title: '负面示例',
      dataIndex: 'negativeExample',
      key: 'negativeExample',
      ellipsis: true,
      render: (text) => (
        <div style={{ maxWidth: 150, whiteSpace: 'nowrap' }}>{text}</div>
      ),
    },
    {
      title: '规则描述',
      dataIndex: 'ruleDesc',
      key: 'ruleDesc',
      ellipsis: true,
      render: (text) => (
        <div style={{ maxWidth: 150, whiteSpace: 'nowrap' }}>{text}</div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'statusCnDesc',
      key: 'status',
      width: 70,
      ellipsis: true,
      render: (status: string) => (
        <Tag color={status === '待发布' ? 'success' : 'processing'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'modifyTime',
      key: 'modifyTime',
      width: 100,
      // ellipsis: true,
      align: 'center',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 190,
      render: (_, record) => (
        <Space size={0}>
          <Button
            type="link"
            size="small"
            style={{ padding: '0 4px' }}
            onClick={() => {
              const initialValues = {
                id: record.id,
                ruleName: record.ruleName,
                keywords: record.keywords,
                positiveExample: record.positiveExample,
                negativeExample: record.negativeExample,
                ruleDesc: record.ruleDesc,
                datasets: record.datasets || [],
              };
              form.setFieldsValue(initialValues);
              setOriginalValues(initialValues);
              setSelectedDatasets(record.datasets || []);
              setIsModalOpen(true);
              setIsViewMode(true);
            }}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            style={{ padding: '0 4px' }}
            onClick={() => {
              const isPublish = record.status === 0;
              Modal.confirm({
                title: isPublish ? '确认发布' : '确认取消发布',
                content: `确定要${isPublish ? '发布' : '取消发布'}规则「${
                  record.ruleName
                }」吗？`,
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                  handleBatchPublish(isPublish ? 1 : 0, [String(record.id)]);
                },
              });
            }}
          >
            {record.status === 0 ? '发布' : '取消发布'}
          </Button>
          <Button
            type="link"
            size="small"
            style={{ padding: '0 4px' }}
            onClick={() => {
              const initialValues = {
                id: record.id,
                ruleName: record.ruleName,
                keywords: record.keywords,
                positiveExample: record.positiveExample,
                negativeExample: record.negativeExample,
                ruleDesc: record.ruleDesc,
                datasets: record.datasets || [],
              };
              form.setFieldsValue(initialValues);
              setOriginalValues(initialValues);
              setSelectedDatasets(record.datasets || []);
              setIsModalOpen(true);
              setIsViewMode(false);
            }}
          >
            修改
          </Button>
          <Divider type="vertical" style={{ margin: '0' }} />
          <Button
            type="link"
            size="small"
            style={{ padding: '0 4px', color: '#ff4d4f' }}
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: `确定要删除规则「${record.ruleName}」吗？`,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  try {
                    await ruleApi.deleteById(record.id);
                    message.success('删除成功');
                    fetchData();
                  } catch (error) {
                    message.error('删除失败');
                  }
                },
              });
            }}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleTableChange = (pagination: any) => {
    setPagination(pagination);
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      // If this is a modification, check for changes
      if (values.id && originalValues) {
        const isChanged = Object.keys(originalValues).some((key) => {
          if (key === 'datasets') {
            return (
              JSON.stringify(originalValues[key].sort()) !==
              JSON.stringify(selectedDatasets.sort())
            );
          }
          return originalValues[key] !== values[key];
        });

        if (!isChanged) {
          message.warning('未发现内容变更');
          return;
        }
      }

      // Remove relatedDatasets from values and use selectedDatasets directly
      const { ...rest } = values;
      const data = {
        ...rest,
        datasets: selectedDatasets,
      };

      const response = values.id
        ? await ruleApi.update(data)
        : await ruleApi.add(data);

      if (response.success) {
        message.success(values.id ? '修改成功' : '新增成功');
        fetchData();
        setIsModalOpen(false);
        form.resetFields();
        setOriginalValues(null);
        setSelectedDatasets([]);
      }
    } catch (error) {
      const hasId = form.getFieldValue('id');
      message.error(hasId ? '修改失败' : '新增失败');
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
    setOriginalValues(null);
    setSelectedDatasets([]);
  };

  // 获取数据集列表
  const fetchDatasetList = async () => {
    setDatasetLoading(true);
    try {
      const response = await ruleApi.fetchDataset();
      if (response.success) {
        setDatasetList(response.result.datasetList);
      }
    } catch (error) {
      message.error('获取数据集列表失败');
    } finally {
      setDatasetLoading(false);
    }
  };

  // 打开弹框
  const handleOpenDatasetModal = () => {
    setIsDatasetModalOpen(true);
  };

  // 确认回调
  const handleDatasetConfirm = (dataset: DatasetItem | null) => {
    if (dataset) {
      message.success(`已选择数据集：${dataset.name}`);
      fetchData(); // 选择成功后刷新列表
    }
    setIsDatasetModalOpen(false);
  };

  const handleRuleAutocomplete = async () => {
    const ruleName = form.getFieldValue('ruleName');
    if (!ruleName) {
      message.warning('请先输入检测项目名称');
      return;
    }
    setRuleAutoLoading(true);
    setShowRuleAutoModal(true);
    try {
      const res = await ruleApi.ruleAutocomplete({ ruleName });
      if (res.success) {
        message.success('"规则辅助生成"成功');
        if (res.result) {
          form.setFieldsValue({
            ruleName: res.result.ruleName,
            keywords: res.result.keywords,
            positiveExample: res.result.positiveExample,
            negativeExample: res.result.negativeExample,
            ruleDesc: res.result.ruleDesc,
          });
        }
      } else {
        message.error(res.message || '"规则辅助生成"失败');
      }
    } catch (e) {
      message.error('"规则辅助生成"失败');
    } finally {
      setRuleAutoLoading(false);
      setShowRuleAutoModal(false);
    }
  };

  return (
    <PageContainer ghost>
      <div className={styles.container}>
        <div className={styles.searchBar}>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={showModal}>
              新增
            </Button>
            <Button
              disabled={selectedRowKeys.length === 0}
              danger
              onClick={() => {
                if (selectedRowKeys.length === 0) {
                  message.warning('请至少选择一条规则');
                  return;
                }
                Modal.confirm({
                  title: '确认批量删除',
                  content: `确定要删除选中的 ${selectedRowKeys.length} 条规则吗？`,
                  okText: '确认',
                  cancelText: '取消',
                  onOk: async () => {
                    let successCount = 0;
                    const total = selectedRowKeys.length;
                    const hide = message.loading(
                      `正在删除 ${total} 条规则...`,
                      0,
                    );

                    try {
                      for (const id of selectedRowKeys) {
                        try {
                          await ruleApi.deleteById(id as string);
                          successCount++;
                        } catch (error) {
                          console.error(`删除ID ${id} 失败:`, error);
                        }
                      }

                      hide();
                      if (successCount === total) {
                        message.success(`成功删除 ${successCount} 条规则`);
                      } else {
                        message.warning(
                          `成功删除 ${successCount} 条，失败 ${
                            total - successCount
                          } 条`,
                        );
                      }
                      fetchData();
                      setSelectedRowKeys([]);
                    } catch (error) {
                      hide();
                      message.error('批量删除过程中出错');
                    }
                  },
                });
              }}
            >
              批量删除
            </Button>
            <Button
              disabled={selectedRowKeys.length === 0}
              loading={publishLoading}
              onClick={() => {
                const ids = selectedRowKeys.map(String);
                const isPublish = data.some(
                  (item) => ids.includes(String(item.id)) && item.status === 0,
                );
                Modal.confirm({
                  title: isPublish ? '确认批量发布' : '确认批量取消发布',
                  content: `确定要${isPublish ? '发布' : '取消发布'}选中的 ${
                    selectedRowKeys.length
                  } 条规则吗？`,
                  okText: '确认',
                  cancelText: '取消',
                  onOk: () => {
                    handleBatchPublish(isPublish ? 1 : 0, ids);
                  },
                });
              }}
            >
              {data.some(
                (item) =>
                  selectedRowKeys.includes(item.id) && item.status === 0,
              )
                ? '发布'
                : '取消发布'}
            </Button>
            <Tooltip title="检测项目一键导入">
              <span
                style={{
                  cursor: 'pointer',
                  color: '#1890ff',
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '18px',
                }}
                onClick={handleOpenDatasetModal}
              >
                <ImportOutlined />
              </span>
            </Tooltip>
          </Space>
          <Input.Search
            placeholder="搜索"
            prefix={<SearchOutlined />}
            style={{ width: 200 }}
            onSearch={(value) => {
              fetchData(value);
            }}
          />
        </div>
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={data}
          pagination={pagination}
          onChange={handleTableChange}
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedKeys) => setSelectedRowKeys(selectedKeys),
          }}
          // scroll={{ x: 'max-content' }}
        />

        <Modal
          title={
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                // justifyContent: 'space-between',
                gap: '8px',
                padding: '10px 0',
              }}
            >
              <span>
                {isViewMode
                  ? '查看检测项'
                  : form.getFieldValue('id')
                  ? '修改检测项'
                  : '新增检测项'}
              </span>
              {!isViewMode && (
                <Tooltip title="检测规则辅助生成">
                  <span
                    style={{
                      cursor: 'pointer',
                      color: '#1890ff',
                      display: 'flex',
                      alignItems: 'center',
                      fontSize: '16px',
                      lineHeight: '16px',
                      // marginRight: 30,
                    }}
                    onClick={handleRuleAutocomplete}
                  >
                    {ruleAutoLoading ? (
                      <LoadingOutlined />
                    ) : (
                      <ExperimentOutlined />
                    )}
                  </span>
                </Tooltip>
              )}
            </div>
          }
          open={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          width={800}
          footer={isViewMode ? null : undefined}
        >
          <Form form={form} layout="vertical" onFinish={handleOk}>
            <Form.Item name="id" hidden>
              <Input disabled={isViewMode} />
            </Form.Item>
            <Form.Item
              label="检测项目"
              name="ruleName"
              rules={[{ required: true, message: '请输入检测项目' }]}
            >
              <Input disabled={isViewMode} />
            </Form.Item>
            <Form.Item label="关联数据集" name="relatedDatasets">
              <div>
                <div className={styles.selectedDatasets}>
                  {selectedDatasets.map((dataset) => (
                    <Tag
                      key={dataset}
                      closable={!isViewMode}
                      onClose={() => {
                        const newDatasets = selectedDatasets.filter(
                          (d) => d !== dataset,
                        );
                        setSelectedDatasets(newDatasets);
                      }}
                    >
                      {dataset === 'market'
                        ? '市场准入负面数据集'
                        : '资质数据集'}
                    </Tag>
                  ))}
                </div>

                {!isViewMode && (
                  <div className={styles.datasetSection}>
                    <div className={styles.datasetHeader}>
                      <span>可选数据集</span>
                      <Space>
                        {isDatasetExpanded ? (
                          <CaretUpOutlined
                            onClick={() => setIsDatasetExpanded(false)}
                          />
                        ) : (
                          <CaretDownOutlined
                            onClick={() => setIsDatasetExpanded(true)}
                          />
                        )}
                      </Space>
                    </div>

                    <div
                      className={styles.datasetContent}
                      style={{ display: isDatasetExpanded ? 'block' : 'none' }}
                    >
                      <div className={styles.datasetGrid}>
                        {[
                          { key: 'market', label: '市场准入负面数据集' },
                          { key: 'qualification', label: '资质数据集' },
                        ].map((dataset) => (
                          <div
                            key={dataset.key}
                            className={`${styles.datasetItem} ${
                              selectedDatasets.includes(dataset.key)
                                ? styles.selected
                                : ''
                            }`}
                            onClick={() => {
                              const newDatasets = selectedDatasets.includes(
                                dataset.key,
                              )
                                ? selectedDatasets.filter(
                                    (d) => d !== dataset.key,
                                  )
                                : [...selectedDatasets, dataset.key];
                              setSelectedDatasets(newDatasets);
                            }}
                          >
                            <DatabaseOutlined style={{ fontSize: 32 }} />
                            <span>{dataset.label}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Form.Item>
            <Form.Item
              label="命中关键词组"
              name="keywords"
              rules={[{ required: true, message: '请输入关键词组' }]}
            >
              <Input disabled={isViewMode} />
            </Form.Item>
            <Form.Item
              label="正面示例"
              name="positiveExample"
              rules={[{ required: true, message: '请输入正面示例' }]}
            >
              <Input.TextArea disabled={isViewMode} />
            </Form.Item>
            <Form.Item
              label="负面示例"
              name="negativeExample"
              rules={[{ required: true, message: '请输入负面示例' }]}
            >
              <Input.TextArea disabled={isViewMode} />
            </Form.Item>
            <Form.Item
              label="检测规则描述"
              name="ruleDesc"
              rules={[{ required: true, message: '请输入规则描述' }]}
            >
              <Input.TextArea disabled={isViewMode} />
            </Form.Item>
          </Form>
        </Modal>

        <DatasetModal
          visible={isDatasetModalOpen}
          onCancel={() => setIsDatasetModalOpen(false)}
          onConfirm={handleDatasetConfirm}
          isViewMode={false}
        />

        <Modal
          open={showRuleAutoModal}
          footer={null}
          closable={false}
          centered
          maskClosable={false}
          width={360}
          style={{ textAlign: 'center', padding: 32 }}
        >
          <LoadingOutlined
            style={{ fontSize: 40, color: '#1890ff', marginBottom: 16 }}
            spin
          />
          <div style={{ fontWeight: 600, fontSize: 20, margin: '16px 0 8px' }}>
            辅助生成中
          </div>
          <div style={{ color: '#666', fontSize: 14 }}>
            系统正在通过{' '}
            <span style={{ color: '#1890ff' }}>公平竞争审查规则</span>{' '}
            辅助生成检测规则，请耐心等待
          </div>
        </Modal>
      </div>
    </PageContainer>
  );
};

export default Manage;
