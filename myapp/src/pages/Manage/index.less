.container {
  padding: 24px;
  background: #fff;
  border-radius: 2px;

  .datasetModalContent {
    padding: 24px 0;

    .notice {
      color: #ff4d4f;
      font-size: 14px;
      margin-bottom: 24px;
    }

    .datasetList {
      display: flex;
      flex-wrap: wrap;
      gap: 20px 24px;
      justify-content: flex-start;
      align-items: flex-start;
      min-height: 160px;
    }

    .datasetCard {
      width: 140px;
      height: 140px;
      background: #f5f7fa;
      border: 1.5px solid #e5e6eb;
      border-radius: 10px;
      box-sizing: border-box;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      box-shadow: none;
      padding: 0;
      overflow: hidden;

      &:hover,
      &.selected {
        border-color: #1890ff;
        background: #e6f7ff;
        box-shadow: 0 0 0 2px #bae7ff;
      }

      .datasetLogo {
        width: 40px;
        height: 40px;
        object-fit: contain;
        display: block;
        margin: 0 auto 12px auto;
      }

      .datasetName {
        color: #333;
        font-size: 14px;
        text-align: center;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 8px;
      }
    }
  }
}

.searchBar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.selectedDatasets {
  // margin-bottom: 16px;
  // min-height: 32px;
  
  :global(.ant-tag) {
    margin-bottom: 8px;
  }
}

.datasetSection {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
}

.datasetHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  // border-bottom: 1px solid #d9d9d9;
}

.datasetContent {
  padding: 16px;
}

.datasetGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.datasetItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  span {
    margin-top: 8px;
    text-align: center;
  }
  
  &.selected {
    background-color: #e6f7ff;
    border: 1px solid #1890ff;
  }
}
