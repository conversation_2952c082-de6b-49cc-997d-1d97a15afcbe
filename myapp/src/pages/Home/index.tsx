import type { UploadResult } from '@/components/FilePreview';
import FilePreview from '@/components/FilePreview';
import UploadFile from '@/components/UploadFile';
import { Button, Card, List, message, Space, Tag } from 'antd';
import { useEffect, useState, useRef } from 'react';
import styles from './index.less';
import { fileApi } from '@/services/api';
import DocumentParsingOverlay from '@/components/FilePreview/DocumentParsingOverlay';

interface UploadHistory {
  id: string;
  fileName: string;
  time: string;
  status: 'success' | 'error';
  error?: string;
  file?: File;
  result?: UploadResult;
}

const HomePage: React.FC = () => {
  const [currentFile, setCurrentFile] = useState<File>();
  const [uploadResult, setUploadResult] = useState<UploadResult>();
  const [uploadHistory, setUploadHistory] = useState<UploadHistory[]>([]);
  const [parsingVisible, setParsingVisible] = useState(false);
  const [parsingStatusDesc, setParsingStatusDesc] = useState('');
  const [parsingProgress, setParsingProgress] = useState(0);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 从localStorage加载历史记录
    const savedHistory = localStorage.getItem('uploadHistory');
    if (savedHistory) {
      setUploadHistory(JSON.parse(savedHistory));
    }
  }, []);

  const saveHistory = (history: UploadHistory) => {
    const newHistory = [history, ...uploadHistory].slice(0, 10); // 保留最近10条
    setUploadHistory(newHistory);
    localStorage.setItem('uploadHistory', JSON.stringify(newHistory));
  };

  const truncateFileName = (fileName: string) => {
    if (fileName.length <= 20) return fileName;
    const ext = fileName.lastIndexOf('.');
    const name = ext !== -1 ? fileName.slice(0, ext) : fileName;
    const extension = ext !== -1 ? fileName.slice(ext) : '';
    return `${name.slice(0, 8)}...${name.slice(-6)}${extension}`;
  };

  // 动态根据 statusList 计算进度
  const calcProgressByStatusList = (status: number, statusList: { status: number }[] = []) => {
    // 新的进度映射规则
    if (status === 1) return 40;
    if (status === 2) return 60;
    if (status === 4) return 100;
    return 0;
  };

  // 轮询文件状态
  const pollFileStatus = (id: string) => {
    setParsingVisible(true);
    const poll = async () => {
      try {
        const res = await fileApi.getFileStatus(id);
        if (res && res.result) {
          const { status, statusCnDesc, statusList } = res.result;
          setParsingStatusDesc(statusCnDesc);
          setParsingProgress(calcProgressByStatusList(status, statusList));
          // 结束条件：成功或失败
          if (status === 4) {
            setParsingVisible(false);
            message.success('知识库创建成功！');
            pollingRef.current && clearTimeout(pollingRef.current);
            return;
          } else if (status === 3 || status === 5) {
            setParsingVisible(false);
            message.error(statusCnDesc || '文档处理失败');
            pollingRef.current && clearTimeout(pollingRef.current);
            return;
          }
        }
        // file-status 轮询时间
        pollingRef.current = setTimeout(poll, 30000);
      } catch (e) {
        setParsingVisible(false);
        message.error('获取文件状态失败');
        pollingRef.current && clearTimeout(pollingRef.current);
      }
    };
    poll();
  };

  useEffect(() => {
    return () => {
      pollingRef.current && clearTimeout(pollingRef.current);
    };
  }, []);

  const handleUploadSuccess = (file: File, result: any) => {
    const historyItem: UploadHistory = {
      id: result.id || Date.now().toString(),
      fileName: file.name,
      time: new Date(result.createTime).toLocaleString('zh-CN'),
      status: 'success',
      file,
      result,
    };
    saveHistory(historyItem);
    setCurrentFile(file);
    setUploadResult(result);
    message.success(`${file.name} 上传成功`);
    setParsingVisible(true);
    setParsingStatusDesc(result.statusCnDesc || '文档上传中...');
    setParsingProgress(10);
    if (result.id) {
      pollFileStatus(result.id);
    }
  };

  const handleUploadError = (error: string, file?: File) => {
    const historyItem: UploadHistory = {
      id: Date.now().toString(),
      fileName: file?.name || '未知文件',
      time: new Date().toLocaleString('zh-CN'),
      status: 'error',
      error,
    };
    saveHistory(historyItem);
    message.error(error);
  };

  const handleDelete = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const newHistory = uploadHistory.filter((item) => item.id !== id);
    setUploadHistory(newHistory);
    localStorage.setItem('uploadHistory', JSON.stringify(newHistory));
    message.success('删除成功');
  };

  const handlePreview = (item: UploadHistory) => {
    if (item.status === 'success' && item.file && item.result) {
      setCurrentFile(item.file);
      setUploadResult(item.result);
    }
  };

  return (
    <div className={styles.container}>
      {/* 解析进度弹窗，始终渲染 */}
      <DocumentParsingOverlay
        visible={parsingVisible}
        statusDesc={parsingStatusDesc}
        progress={parsingProgress}
      />
      {!currentFile ? (
        <>
          <UploadFile
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
          />
          <Card style={{ marginTop: 16 }} title="上传历史记录">
            <List
              dataSource={uploadHistory}
              renderItem={(item) => (
                <List.Item
                  onClick={() => handlePreview(item)}
                  style={{
                    cursor: item.status === 'success' ? 'pointer' : 'default',
                    padding: '10px',
                    backgroundColor:
                      currentFile && item.file && currentFile === item.file
                        ? '#f0f7ff'
                        : 'transparent',
                    borderRadius: 4,
                    transition: 'background-color 0.3s',
                  }}
                >
                  <List.Item.Meta
                    title={
                      <Space direction="vertical" size={0}>
                        <Space>
                          <span>{truncateFileName(item.fileName)}</span>
                          <span style={{ fontSize: 12, color: '#999' }}>
                            {item.time}
                          </span>
                          <Tag
                            color={item.status === 'success' ? 'green' : 'red'}
                          >
                            {item.status === 'success' ? '成功' : '失败'}
                          </Tag>
                        </Space>
                        <Space>
                          {item.error && (
                            <span
                              style={{ fontSize: 12, color: '#ff4d4f' }}
                            ></span>
                          )}
                        </Space>
                      </Space>
                    }
                  />
                  <Button
                    type="text"
                    danger
                    onClick={(e: React.MouseEvent) => handleDelete(item.id, e)}
                  >
                    删除
                  </Button>
                </List.Item>
              )}
            />
          </Card>
        </>
      ) : (
        <FilePreview
          file={currentFile}
          uploadResult={uploadResult}
          onFileSelect={() => setCurrentFile(undefined)}
        />
      )}
    </div>
  );
};

export default HomePage;
