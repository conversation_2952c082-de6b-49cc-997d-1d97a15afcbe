.container {
  // padding: 24px;
  // background: #fff;
  // min-height: 400px;
}

.preview {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .fileInfo {
    h3 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;
    }

    p {
      margin-bottom: 8px;
      color: #666;
    }
  }

  .previewContent {
    flex: 1;
    min-height: 300px;
    border: 1px dashed #d9d9d9;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.emptyPreview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 600px;
  background-color: #f5f5f5;
  border-radius: 2px;

  p {
    color: #999;
    font-size: 14px;
  }
}

.pdfViewer {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 2px;
  min-height: 600px;
  overflow: auto;

  canvas {
    max-width: 100%;
    margin: 0 auto;
    display: block;
  }
}

.pdfControls {
  margin-top: 16px;
  text-align: center;

  button {
    margin: 0 8px;
  }

  .pageInfo {
    margin: 0 16px;
    color: rgba(0, 0, 0, 85%);
  }
}
.ant-pro-layout-container {
  .ant-pro-layout {
    .ant-pro-layout-content{
      padding-block: 0 !important;
      padding-inline: 0 !important;
}
  }
}