export default {
  hash: true, // 添加文件名哈希防止缓存
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  base: '/',
  layout: {
    title: '招标文件\n公平性竞争智能检查',
  },
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      name: '智能检测',
      path: '/home',
      component: './Home',
    },
    {
      name: '检测管理',
      path: '/manage',
      component: './Manage',
    },
  ],
  npmClient: 'pnpm',
};
