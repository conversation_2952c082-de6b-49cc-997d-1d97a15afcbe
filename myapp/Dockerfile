# 定义构建参数
ARG PNPM_VERSION=latest

# 第一阶段：构建阶段
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 启用PNPM并安装指定版本
RUN corepack enable && corepack prepare pnpm@${PNPM_VERSION} --activate

# 复制依赖文件并安装
COPY package.json pnpm-lock.yaml ./
RUN pnpm config set registry https://registry.npmmirror.com/ \
    && pnpm install --fetch-timeout 1000000 \
    && pnpm cache clean

# 复制项目文件并构建
COPY . .
RUN pnpm build && \
    rm -rf node_modules && \
    ls -la /app/dist && \
    find /app/dist -type f

# 第二阶段：生产镜像
FROM nginx:1.28-alpine

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 从构建阶段复制产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx服务
CMD ["nginx", "-g", "daemon off;"]
